import json
import os
from tqdm import tqdm

# 需要处理的所有目录（与 generate_data_v3.py 中的 data_dirs 保持一致）
data_dirs = [
    "Topo2text_data/已修改数据/easy/中文",
    "Topo2text_data/已修改数据/easy/英文",
    "Topo2text_data/已修改数据/normal中英/中文",
    "Topo2text_data/已修改数据/normal中英/英文"
]

image_base_dir = "data/images"
output_file = "Topo2text_data/merged_summary.json"

def find_image_path(file_name):
    """复用 generate_data_v3.py 中的图片查找逻辑（第15-32行）"""
    sub_dirs = ["easy", "normal", "hard"]
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    
    for sub_dir in sub_dirs:
        search_dir = os.path.join(image_base_dir, sub_dir)
        for ext in image_extensions:
            potential_path = os.path.join(search_dir, file_name + ext)
            if os.path.exists(potential_path):
                return potential_path
    return None

all_conversations = []

for data_dir in tqdm(data_dirs, desc="处理目录"):
    if not os.path.exists(data_dir):
        print(f"跳过不存在目录: {data_dir}")
        continue
        
    for filename in sorted(os.listdir(data_dir)):
        if filename.endswith('.json'):
            file_path = os.path.join(data_dir, filename)
            file_name = os.path.splitext(filename)[0]
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    if data.get('qa_pairs'):
                        # 获取最后一个QA对（第38行逻辑修改）
                        last_qa = data['qa_pairs'][-1]
                        
                        # 构建标准格式（第43-53行逻辑）
                        conversation = {
                            "conversations": [
                                {"from": "human", "value": last_qa["question"]},
                                {"from": "gpt", "value": last_qa["answer"]}
                            ],
                            "images": find_image_path(file_name)
                        }
                        all_conversations.append(conversation)
                        
            except Exception as e:
                print(f"处理文件 {file_path} 出错: {str(e)}")

# 保持与 generate_data_v3.py 相同的输出方式（第80-83行）
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(all_conversations, f, ensure_ascii=False, indent=2)

print(f"成功生成 {len(all_conversations)} 个对话，输出文件: {output_file}")
