{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folder: data/images/easy, Image count: 802\n", "Folder: data/images/normal, Image count: 1095\n", "Folder: data/images/hard, Image count: 743\n", "Total image count across all folders: 2640\n"]}], "source": ["import os\n", "\n", "\n", "def count_images_in_folder(folder_path):\n", "    image_extensions = {\".png\", \".jpg\", \".jpeg\", \".gif\", \".bmp\", \".webp\"}\n", "    total_images = 0\n", "\n", "    for root, dirs, files in os.walk(folder_path):\n", "        for file in files:\n", "            _, ext = os.path.splitext(file)\n", "            if ext.lower() in image_extensions:\n", "                total_images += 1\n", "\n", "    return total_images\n", "\n", "\n", "def main():\n", "    folders = [\n", "        \"data/images/easy\",\n", "        \"data/images/normal\",\n", "        \"data/images/hard\",\n", "    ]\n", "\n", "    total_count = 0\n", "\n", "    for folder in folders:\n", "        if os.path.exists(folder):\n", "            count = count_images_in_folder(folder)\n", "            print(f\"Folder: {folder}, Image count: {count}\")\n", "            total_count += count\n", "        else:\n", "            print(f\"Folder not found: {folder}\")\n", "\n", "    print(f\"Total image count across all folders: {total_count}\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "topo_qwen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}