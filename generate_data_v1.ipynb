{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved data to /home/<USER>/llm2024/topo2text/LLaMA-Factory/data/mllm_topo2text_v2.json\n"]}], "source": ["import json\n", "import os\n", "\n", "\n", "def find_image_file(txt_file_path, res_directory):\n", "    file_name_without_extension = os.path.splitext(os.path.basename(txt_file_path))[0]\n", "    image_extensions = [\n", "        \".jpg\",\n", "        \".jpeg\",\n", "        \".png\",\n", "        \".gif\",\n", "        \".bmp\",\n", "        \".tiff\",\n", "        \".webp\",\n", "        \".svg\",\n", "    ]\n", "\n", "    for extension in image_extensions:\n", "        image_file_path = os.path.join(\n", "            res_directory, file_name_without_extension + extension\n", "        )\n", "        if os.path.exists(image_file_path):\n", "            return image_file_path\n", "    return None\n", "\n", "def replace_image_tag_with_filename(txt_content, image_file_path):\n", "    if not image_file_path:\n", "        # 如果图片不存在，直接返回None\n", "        return None\n", "\n", "    json_structure = {\n", "        \"conversations\": [\n", "            {\"from\": \"human\", \"value\": \"\"},\n", "            {\"from\": \"gpt\", \"value\": txt_content},\n", "        ],\n", "        \"images\": image_file_path,\n", "    }\n", "\n", "    json_structure[\"conversations\"][0][\n", "        \"value\"\n", "    ] = f\"Summary the topology image below: <image>\"\n", "\n", "    return json_structure\n", "\n", "\n", "directory = \"data/res_summary_len8\"\n", "pic_directory = \"/home/<USER>/llm2024/topo2text/data/images/normal\"\n", "save_directory = \"/home/<USER>/llm2024/topo2text/LLaMA-Factory/data/\"\n", "\n", "output_json_path = os.path.join(save_directory, \"mllm_topo2text_v2.json\")\n", "\n", "with open(output_json_path, \"w\", encoding=\"utf-8\") as json_file:\n", "    json_file.write(\"[\")\n", "\n", "    first_entry = True\n", "    for filename in os.listdir(directory):\n", "        if filename.endswith(\".txt\"):\n", "            txt_file_path = os.path.join(directory, filename)\n", "\n", "            with open(txt_file_path, \"r\", encoding=\"utf-8\") as file:\n", "                txt_content = file.read()\n", "\n", "            image_file_path = find_image_file(txt_file_path, pic_directory)\n", "\n", "            output_json_data = replace_image_tag_with_filename(\n", "                txt_content, image_file_path\n", "            )\n", "\n", "            if output_json_data is not None:\n", "                if not first_entry:\n", "                    json_file.write(\",\\n\")\n", "                first_entry = False\n", "\n", "                json.dump(output_json_data, json_file, ensure_ascii=False, indent=2)\n", "\n", "    json_file.write(\"\\n]\")\n", "\n", "print(f\"Saved data to {output_json_path}\")"]}], "metadata": {"kernelspec": {"display_name": "ccks2024", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}