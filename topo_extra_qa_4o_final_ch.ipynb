{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import base64\n", "import json\n", "import os\n", "import random\n", "from openai import OpenAI\n", "from pydantic import BaseModel\n", "from typing import List, Dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义图像编码函数\n", "- 4o系列：请使用Base64编码将图片转换为字符串格式。\n", "- QwenVL系列：可以直接发送原始图片数据，无需进行编码。"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def encode_image(image_path):\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义QA对"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["class QA_Pair(BaseModel):\n", "    question: str\n", "    answer: str"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "##### 类定义：`Cmanager`\n", "`Cmanager`类是一个用于管理网络拓扑图像和相关操作的类。它提供了初始化客户端、设置拓扑图像路径、提取实体、构建问答对以及保存数据到JSON文件的功能。\n", "属性：\n", "- `client`: 用于与API通信的客户端对象。\n", "- `topology_image_path`: 存储拓扑图像文件路径的字符串。\n", "方法：\n", "1. `__init__(self, api_base: str, api_key: str)`：\n", "   - 初始化方法，接收两个参数：`api_base`（API的基础URL）和`api_key`（API的密钥）。\n", "   - 创建客户端实例并存储在`self.client`中。\n", "   - 初始化`topology_image_path`为空字符串。\n", "2. `get_client(self, base_url, api_key)`：\n", "   - 私有方法，用于创建并返回一个`OpenAI`客户端实例。\n", "   - 参数：`base_url`和`api_key`。\n", "3. `set_topology_image(self, image_path: str)`：\n", "   - 设置拓扑图像文件路径。\n", "   - 参数：`image_path`（图像文件的路径）。\n", "4. `extract_entities(self) -> List[str]`：\n", "   - 提取拓扑图像中的网络实体。\n", "   - 检查`topology_image_path`是否已设置，如果没有，则抛出`ValueError`。\n", "   - 将图像编码为base64格式，并构建系统内容字符串。\n", "   - 使用`gpt-4o-mini`模型创建聊天完成请求，提取实体并返回实体列表。\n", "5. `build_qa_pairs(self, entities: List[str], num_pairs: int = 10) -> List[Dict[str, str]]`：\n", "   - 基于提取的实体构建问答对。\n", "   - 参数：`entities`（实体列表），`num_pairs`（要生成的问答对数量，默认为10）。\n", "   - 随机选择实体和问题模板，构建问题并使用`gpt-4o-mini`模型生成答案。\n", "   - 返回包含问题和答案的字典列表。\n", "6. `save_to_json(self, entities: List[str], qa_pairs: List[Dict[str, str]], filename: str)`：\n", "   - 将实体和问答对保存为JSON文件。\n", "   - 参数：`entities`（实体列表），`qa_pairs`（问答对列表），`filename`（要保存的文件名）。\n", "\n", "##### 注意事项：\n", "1. **图像路径设置**：在调用`extract_entities`和`build_qa_pairs`方法之前，必须通过`set_topology_image`方法设置拓扑图像路径。\n", "2. **图像编码**：`extract_entities`和`build_qa_pairs`方法中使用了`encode_image`函数对图像进行base64编码，确保该函数已正确实现。\n", "3. **问答对生成**：`build_qa_pairs`方法默认生成10个问答对，可以根据需要调整`num_pairs`参数。\n", "4. **JSON文件保存**：`save_to_json`方法将实体和问答对保存为JSON文件，确保指定的`filename`路径可写。\n", "5. **随机性**：`build_qa_pairs`方法中使用了随机选择实体和问题模板，这可能导致每次生成的问答对不同。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["## 定义Cmanager类\n", "class Cmanager:\n", "    def __init__(self, api_base: str, api_key: str):\n", "        self.client = self.get_client(api_base, api_key)\n", "        self.topology_image_path = \"\"\n", "\n", "    def get_client(self, base_url, api_key):\n", "        client = OpenAI(base_url=base_url, api_key=api_key)\n", "        return client\n", "\n", "    def set_topology_image(self, image_path: str):\n", "        self.topology_image_path = image_path\n", "\n", "    # STEP1 抽取实体\n", "    def extract_entities(self) -> List[str]:\n", "        if not self.topology_image_path:\n", "            raise ValueError(\"Topology image path is not set.\")\n", "        base64_image = encode_image(self.topology_image_path)\n", "\n", "        content_system = (\n", "            \"你是一个网络拓扑实体提取专家，专门识别命名的核心网络元素。\"\n", "            \"你将收到一张描绘网络拓扑的图像。\"\n", "            \"你的任务是从图像中识别命名的核心网络元素。\"\n", "            \"从命名的核心网络元素中提取最多八个（八 (8) 个最重要的命名核心网络元素）。\"\n", "            \"如果命名的核心网络元素少于八个，则输出所有这些元素。\"\n", "            \"如果图像中没有至少一个命名的核心网络元素，则输出空列表：[]。\"\n", "            \"1. 核心网络元素：\"\n", "            \"  - 核心网络元素是指路由器、交换机、服务器、防火墙等必要组件，这些组件在图像中有明确的名称。\"\n", "            \"2. 输出格式：\"\n", "            \"  - 输出核心网络元素的名称（而不是类型），作为字符串列表。不要使用其他格式。\"\n", "            \"  - 不要包含任何解释、分析或额外信息。\"\n", "            f\"  - 对于带有命名元素的图像的示例输出：['R1', 'SW1', 'ServerA']\"\n", "            f\"  - 对于没有命名元素的图像的示例输出：[]\"\n", "            \"3. 重要说明：\"\n", "            \"  - 仅关注命名实体。未命名的元素应被忽略。\"\n", "            \"  - 提取最多八个核心网络元素名称（不同名称）。\"\n", "            \"  - 输出提取的名称与图像中的显示完全一致。\"\n", "            \"  - 确保输出为有效列表。\"\n", "            # \"You are a network topology entity extraction expert specialized in identifying named key network elements.\"\n", "            # \"You will receive an image depicting a network topology.\"\n", "            # \"Your task is to identify **named** core network elements from the image.\"\n", "            # \"Extract a maximum of eight (**eight (8) most crucial named core network elements**) of the core network elements from the named ones.\"\n", "            # \"If there are less than eight named core network elements, output all of them.\"\n", "            # \"If the image does not contain at least one named core network element, output an empty list: `[]`.\"\n", "            # \"1. Core Network Elements:\"\n", "            # \"  - Core network elements are essential components like routers, switches, servers, firewalls, etc., that have a distinct name in the image.\"\n", "            # \"2. Output Format:\"\n", "            # \"  - Output the core network element **names** (not the types) as a list of strings. Do not use any other format.\"\n", "            # \"  - Do not include any explanations, analysis, or additional information.\"\n", "            # f\"  - Example output for an image with named elements: ['R1', 'SW1', 'ServerA']\"\n", "            # f\"  - Example output for an image with no named elements: []\"\n", "            # \"3. Important Notes:\"\n", "            # \"  - **Focus solely on named entities. Unnamed elements should be ignored.**\"\n", "            # \"  - Extract a **maximum of eight** core network element names(different names).\"\n", "            # \"  - Output the extracted names **exactly as they appear** in the image.\"\n", "            # \"  - Ensure the output is a valid list.\"\n", "        )\n", "\n", "        completion = self.client.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": content_system},\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\"type\": \"text\", \"text\": \"拓扑结构图\"},\n", "                        {\n", "                            \"type\": \"image_url\",\n", "                            \"image_url\": {\n", "                                \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                            },\n", "                        },\n", "                    ],\n", "                },\n", "            ],\n", "            temperature=0.2,\n", "        )\n", "\n", "        # response_content = completion.choices[0].message.content.strip()\n", "\n", "        # list_str = response_content[2:-2]\n", "        # entities = [entity.strip(\"'\") for entity in list_str.split(\", \")]\n", "        # print(\"Raw response:\", response_content)\n", "        # return entities\n", "        response_content = completion.choices[0].message.content.strip()\n", "        try:\n", "            list_str = response_content[2:-2]\n", "            entities = [entity.strip(\"'\") for entity in list_str.split(\", \")]\n", "            if not all(isinstance(entity, str) for entity in entities):\n", "                raise ValueError(\"Entities are not in the correct format.\")\n", "            return entities\n", "        except Exception as e:\n", "            print(f\"Error processing entities: {e}\")\n", "            return []\n", "\n", "    # STEP2 生成QA对\n", "\n", "    def build_qa_pairs(\n", "        self, entities: List[str], num_pairs: int = 5\n", "    ) -> List[Dict[str, str]]:\n", "        qa_pairs = []\n", "        base64_image = encode_image(self.topology_image_path)\n", "        \n", "        question_templates = [\n", "            \"请描述整体网络拓扑类型。它是星形、总线、环形、网状、树形还是混合拓扑？如果是混合拓扑，请具体说明组合方式。\",\n", "            \"请指定网络拓扑图中的节点总数。随后，基于这个节点总数，列出网络拓扑中的主要节点，包括它们的具体名称或标识符，并简要描述每个主要节点的功能（如果主要节点没有名称，请根据拓扑图为它们分配名称）。\",\n", "            \"请详细描述主要节点之间的连接，包括连接类型（例如，有线、无线、光纤）和连接关系（例如，一对一、一对多、多对多）。\",\n", "            \"在此拓扑中，哪个节点是中心节点？为什么该节点被视为中心节点？（如果中心节点有名称，请根据拓扑图分配一个名称。）\",\n", "            \"请简要总结网络拓扑，控制字数在以下限制内：少于10个节点时不超过100个字，10到15个节点时不超过200个字，超过15个节点时不超过300个字。总结应包括以下内容：\\n\\n*   **整体架构：** 星形、总线、环形、网状、树形或混合。\\n*   **区域/设备描述：** 描述每个主要区域或设备（例如，服务器、路由器、交换机、客户端等），包括它们的数量、类型和主要目的。\\n*   **连接方式：** 描述区域/设备之间的连接类型（例如，有线、无线、光纤）和连接关系（哪些节点相互连接）。\\n*   **数据流：** 概述数据或控制信息在网络中的流动方式。\\n*   **网络目标：** 总结网络架构的主要设计目标和功能，例如数据传输、资源共享、安全访问等。\\n\\n使用以下结构进行总结：\\n\\n该网络是一个[整体架构]拓扑。它由几个相互连接的区域和设备组成。首先，[区域/设备名称1]包括[数量][设备类型]，并通过[连接方式]相连。该区域的主要目的是[区域/设备1的目的]。它通过[连接类型]与[区域/设备名称2]相连，促进数据和控制流。[区域/设备名称2]由[数量][设备类型]组成，作为[区域/设备2的目的]。此外，[区域/设备名称2]还连接到其他区域/设备，如[区域/设备名称3]，形成整体网络结构。区域/设备之间的连接还包括[连接类型，例如，防火墙、VPN连接]，提供[连接的目的]。该网络旨在实现[整体网络目的，例如，高效的数据处理、安全的远程访问]。\",\n", "        ]\n", "\n", "        # question_templates = [\n", "        #     # TODO 增加QA类型，不仅仅局限在两个节点之间的联系\n", "        #     \"How are {entity1} and {entity2} connected? Describe any dependencies between them.\",\n", "        #     \"Explain the relationship between these network elements: {entities}.\",\n", "        #     \"Based on the topology, which network elements can be considered a group? What common characteristics do they share?\",\n", "        #     \"If you need to isolate {entity1} and {entity2} into separate network zones, how would you achieve this?\",\n", "        #     \"If {entity1} were to fail, which other network elements would be affected and how?\",\n", "        #     \"What is the path data takes when traveling from {entity1} to {entity2}?\",\n", "        #     \"If a new network element needs to be added to connect {entity1} and {entity2}, where would you place it and why?\",\n", "        #     \"Identify any potential bottlenecks or single points of failure in the connection between {entity1} and {entity2}.\",\n", "        #     \"Describe the security implications of the connection between {entity1} and {entity2}.\",\n", "        #     \"How does the placement of {entity1} and {entity2} affect the overall performance and reliability of the network?\",\n", "        #     \"Assuming {entity1} is a web server and {entity2} is a client, describe the communication flow between them.\",\n", "        #     \"If you wanted to increase the bandwidth between {entity1} and {entity2}, what changes could you make to the topology?\",\n", "        #     \"What protocols are likely being used for communication between {entity1} and {entity2}?\",\n", "        #     \"How can you monitor the traffic flow and performance of the connection between {entity1} and {entity2}?\",\n", "        #     \"If there is a security breach at {entity1}, how could it potentially impact {entity2}?\",\n", "        # ]\n", "\n", "        # if entities == ['']:\n", "        #     print(f\"{entities} is an empty list.\")\n", "        #     question_templates = [\n", "        #         \"What is the overall topology of this network? Is it a star, bus, ring, mesh, or hybrid topology?\",\n", "        #         \"What are the key network segments in this topology, and what are their purposes?\",\n", "        #         \"Describe the role of the main network elements in this network and how they interact with each other.\",\n", "        #         \"How would you implement a backup strategy for the main network elements, considering their relations with each other?\",\n", "        #         \"What are some potential security vulnerabilities in this network, particularly concerning the interactions between the main network elements, and how would you address them?\",\n", "        #         ]\n", "        # else:\n", "        #     question_templates = [\n", "        #         \"What is the overall topology of this network? Is it a star, bus, ring, mesh, or hybrid topology?\",\n", "        #         \"What are the key network segments in this topology, and what are their purposes?\",\n", "        #         \"Describe the role of the {entity} in this network and how it interacts with other entities.\",\n", "        #         \"How would you implement a backup strategy for the {entity}, considering its relations with other critical entities in the network?\",\n", "        #         \"What are some potential security vulnerabilities in this network, particularly concerning the interactions between {entity1} and {entity2}, and how would you address them?\",\n", "        #     ]\n", "\n", "        # question_templates = [\n", "        #     # TODO 增加QA类型，不仅仅局限在两个节点之间的联系\n", "        #     \"What is the overall topology of this network? Is it a star, bus, ring, mesh, or hybrid topology?\",\n", "        #     \"What are the key network segments in this topology, and what are their purposes?\",\n", "        #     \"Describe the role of the {entity} in this network.\",\n", "        #     \"How would you implement a backup strategy for the {entity}?\",\n", "        #     \"What are some potential security vulnerabilities in this network, and how would you address them?\",\n", "        #     \"How would you ensure high availability for critical services on this network?\",\n", "        #     \"What are the advantages and disadvantages of using a {entity} in this topology?\",\n", "        #     \"How would you perform network troubleshooting in case of a connectivity issue between {entity1} and {entity2}?\",\n", "        #     \"How would you configure VLANs in this network to improve security and performance?\",\n", "        #     \"What kind of routing protocols (e.g., RIP, OSPF, BGP) might be used in this network?\",\n", "        #     \"How would you implement access control policies for different types of users in this network?\",\n", "        #     \"What are the main differences between the {entity1} and {entity2} in this topology?\",\n", "        #     \"How would you measure network latency between different segments of this network?\",\n", "        #     \"What is the maximum bandwidth capacity of the link between {entity1} and {entity2}?\",\n", "        #     \"How would you monitor the health of the {entity} in this topology?\",\n", "        #     \"What are the potential consequences of a failure in the {entity}?\",\n", "        #     \"Describe the data flow path for a user accessing the internet from a client within this network.\",\n", "        #     \"How would you implement a guest network in this topology?\",\n", "        #     \"What are the best practices for managing and maintaining this network?\",\n", "        #     \"How would you analyze network traffic patterns in this topology?\",\n", "        #     \"Explain the purpose of the firewall in this network.\",\n", "        #     \"What are the potential challenges in managing this network, and how would you overcome them?\",\n", "        #     \"How would you improve the overall performance of this network?\",\n", "        #     \"What are some common troubleshooting steps for a network outage in this topology?\",\n", "        #     \"What considerations should be made when adding a new device to this network?\",\n", "        #     \"How would you implement load balancing across multiple servers in this network?\",\n", "        #     \"What are the best practices for network security in this topology?\",\n", "        #     \"How would you ensure data integrity in this network?\",\n", "        #     \"How would you configure network address translation (NAT) in this topology?\",\n", "        #     \"How would you implement a VPN in this network?\",\n", "        #     \"How do the network devices in this topology interact with each other?\",\n", "        #     \"What are the main protocols used in this network?\",\n", "        #     \"What are the potential use cases for this network setup?\",\n", "        #     \"How would you segment this network into multiple subnets?\",\n", "        #     \"What are the potential benefits of using cloud-based services in conjunction with this network?\",\n", "        #     \"How could you leverage network automation tools to manage this infrastructure?\",\n", "        #     \"Identify the most critical devices in this topology and explain why they are critical.\",\n", "        #     \"Describe how the network traffic is managed and prioritized in this network.\",\n", "        #     \"Summarize the topology diagram.\",\n", "        # ]\n", "\n", "        # num_pairs = min(num_pairs, len(question_templates))\n", "\n", "        max_pairs = min(num_pairs, len(question_templates))\n", "\n", "        for i in range(max_pairs):\n", "            question_template = question_templates[i]\n", "            question = question_template\n", "\n", "            content_system = (\n", "                \"你是一位网络拓扑专家助手。\"\n", "                \"你的任务是根据提供的图像和关键节点实体列表回答有关网络拓扑的问题。\"\n", "                \"指示：\"\n", "                \"1. 仔细检查拓扑图像及相应的关键节点实体：理解关键节点实体之间的连接和关系，并使用实体名称来识别组件。\"\n", "                \"2. 准确简洁地回答问题：提供清晰直接的答案，不做假设或引入外部信息。\"\n", "                \"3. 将你的答案输出为字符串。\"\n", "                \"拓扑图像：\"\n", "                \"实体：\"\n", "                f\"{entities} \"\n", "                \"问题：\"\n", "                f\"{question}\"\n", "            )\n", "\n", "            completion = self.client.chat.completions.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": content_system},\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\"type\": \"text\", \"text\": \"拓扑结构图\"},\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                                },\n", "                            },\n", "                            {\"type\": \"text\", \"text\": f\"{question}\"},\n", "                        ],\n", "                    },\n", "                ],\n", "                temperature=0.2,\n", "                max_tokens=4095,\n", "            )\n", "            answer = completion.choices[0].message.content.strip()\n", "\n", "            qa_pairs.append({\"question\": question, \"answer\": answer})\n", "\n", "        return qa_pairs\n", "\n", "    def save_to_json(\n", "        self, entities: List[str], qa_pairs: List[Dict[str, str]], filename: str\n", "    ):\n", "        data = {\"entities\": entities, \"qa_pairs\": qa_pairs}\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(data, f, indent=4, ensure_ascii=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化Cmanager实例并设置网络拓扑图像路径"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["api_base = \"https://neudm.zeabur.app/v1\"\n", "api_key = \"sk-T05m0OqxOgKUjErs8c231e1c02E24573A17977F5E839E91c\"\n", "cmanager = Cmanager(api_base=api_base, api_key=api_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub_paths = [\"normal\"]\n", "data_path = \"data/images/\"\n", "res_path = \"data/result/unchecked/\"\n", "\n", "for sub in sub_paths:\n", "    topology_image_path = os.path.join(data_path, sub)\n", "\n", "    files = [\n", "        filename\n", "        for filename in os.listdir(topology_image_path)\n", "        if os.path.isfile(os.path.join(topology_image_path, filename))\n", "    ]\n", "    files.sort()\n", "\n", "    for i, filename in enumerate(files[501:1000]):\n", "        file_path = os.path.join(topology_image_path, filename)\n", "\n", "        image_name_without_ext = os.path.splitext(filename)[0]\n", "        output_filename = f\"{image_name_without_ext}.json\"\n", "        result_dir = os.path.join(res_path, sub)\n", "\n", "        if not os.path.exists(result_dir):\n", "            os.makedirs(result_dir)\n", "\n", "        output_file_path = os.path.join(result_dir, output_filename)\n", "\n", "        # 检查输出文件是否已存在，若存在则跳过对该图像的处理\n", "        if os.path.exists(output_file_path):\n", "            print(f\"File {output_file_path} already exists. Skipping...\")\n", "            continue  \n", "\n", "        # 如果文件不存在，则进行图像处理\n", "        cmanager.set_topology_image(file_path)\n", "        try:\n", "            entities = cmanager.extract_entities()\n", "            if entities:\n", "                print(\"Extracted network element entities:\", entities)\n", "                qa_pairs = cmanager.build_qa_pairs(entities)\n", "                print(\"Built QA pairs:\", qa_pairs)\n", "\n", "                cmanager.save_to_json(entities, qa_pairs, output_file_path)\n", "                print(f\"Results saved to {output_file_path}\")\n", "        except Exception as e:\n", "            print(f\"Error processing file {file_path}: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}