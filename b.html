<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2932.703125 1042" style="max-width: 2932.703125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12"><style>#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .error-icon{fill:#a44141;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-thickness-normal{stroke-width:1px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .marker.cross{stroke:lightgrey;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 p{margin:0;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster-label text{fill:#F9FFFE;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster-label span{color:#F9FFFE;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster-label span p{background-color:transparent;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .label text,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 span{fill:#ccc;color:#ccc;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node rect,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node circle,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node ellipse,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node polygon,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .rough-node .label text,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node .label text,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .image-shape .label,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .icon-shape .label{text-anchor:middle;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .rough-node .label,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node .label,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .image-shape .label,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .icon-shape .label{text-align:center;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .node.clickable{cursor:pointer;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .arrowheadPath{fill:lightgrey;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster text{fill:#F9FFFE;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .cluster span{color:#F9FFFE;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 rect.text{fill:none;stroke-width:0;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .icon-shape,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .icon-shape p,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .icon-shape rect,#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="529" width="1042.25" y="450" x="1882.453125" style=""></rect><g transform="translate(2343.1796875, 450)" class="cluster-label"><foreignObject height="24" width="120.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>6️⃣ 推理评估阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="703" width="310" y="331" x="1522.453125" style=""></rect><g transform="translate(1617.0546875, 331)" class="cluster-label"><foreignObject height="24" width="120.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>5️⃣ 模型集成阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="691" width="310" y="237" x="1162.453125" style=""></rect><g transform="translate(1249.0546875, 237)" class="cluster-label"><foreignObject height="24" width="136.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>4️⃣ 数据集划分阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="534" width="310" y="125" x="802.453125" style=""></rect><g transform="translate(897.0546875, 125)" class="cluster-label"><foreignObject height="24" width="120.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>3️⃣ 编码处理阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="655" width="384.453125" y="31" x="368" style=""></rect><g transform="translate(499.828125, 31)" class="cluster-label"><foreignObject height="24" width="120.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>2️⃣ 路径管理阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="396" width="310" y="8" x="8" style=""></rect><g transform="translate(94.6015625, 8)" class="cluster-label"><foreignObject height="24" width="136.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>1️⃣ 图片预处理阶段</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A1_B1_0" d="M274.547,106L281.789,106C289.031,106,303.516,106,314.924,106C326.333,106,334.667,106,343,106C351.333,106,359.667,106,369.71,108.874C379.754,111.748,391.507,117.495,397.384,120.369L403.261,123.243"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A2_B1_1" d="M293,294L297.167,294C301.333,294,309.667,294,318,294C326.333,294,334.667,294,343,294C351.333,294,359.667,294,369.71,291.126C379.754,288.252,391.507,282.505,397.384,279.631L403.261,276.757"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B1_C1_2" d="M727.453,200L731.62,200C735.786,200,744.12,200,752.453,200C760.786,200,769.12,200,777.453,200C785.786,200,794.12,200,802.367,202.79C810.614,205.581,818.774,211.161,822.854,213.952L826.934,216.742"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B2_C1_3" d="M713.289,400L719.816,400C726.344,400,739.398,400,750.092,400C760.786,400,769.12,400,777.453,400C785.786,400,794.12,400,801.883,397.819C809.646,395.638,816.84,391.275,820.436,389.094L824.033,386.913"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B3_C1_4" d="M690.102,588L700.493,588C710.885,588,731.669,588,746.228,588C760.786,588,769.12,588,777.453,588C785.786,588,794.12,588,815.829,556.084C837.538,524.168,872.623,460.337,890.165,428.421L907.707,396.505"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_D1_5" d="M1087.453,306L1091.62,306C1095.786,306,1104.12,306,1112.453,306C1120.786,306,1129.12,306,1137.453,306C1145.786,306,1154.12,306,1167.285,311.805C1180.449,317.61,1198.446,329.221,1207.444,335.026L1216.442,340.831"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_D1_6" d="M1087.328,561L1091.516,561C1095.703,561,1104.078,561,1112.432,561C1120.786,561,1129.12,561,1137.453,561C1145.786,561,1154.12,561,1173.148,546.138C1192.177,531.276,1221.901,501.552,1236.763,486.69L1251.625,471.828"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_E1_7" d="M1435.313,406L1441.503,406C1447.693,406,1460.073,406,1470.43,406C1480.786,406,1489.12,406,1497.453,406C1505.786,406,1514.12,406,1522.938,408.821C1531.756,411.642,1541.06,417.284,1545.711,420.105L1550.363,422.926"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_E1_8" d="M1447.453,594L1451.62,594C1455.786,594,1464.12,594,1472.453,594C1480.786,594,1489.12,594,1497.453,594C1505.786,594,1514.12,594,1522.938,591.179C1531.756,588.358,1541.06,582.716,1545.711,579.895L1550.363,577.074"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_E1_9" d="M1447.453,806L1451.62,806C1455.786,806,1464.12,806,1472.453,806C1480.786,806,1489.12,806,1497.453,806C1505.786,806,1514.12,806,1537.487,768.095C1560.854,730.189,1599.255,654.379,1618.455,616.474L1637.655,578.568"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_F1_10" d="M1807.453,500L1811.62,500C1815.786,500,1824.12,500,1832.453,500C1840.786,500,1849.12,500,1857.453,500C1865.786,500,1874.12,500,1900.652,538.258C1927.184,576.516,1971.914,653.031,1994.279,691.289L2016.645,729.547"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_F1_11" d="M1807.453,712L1811.62,712C1815.786,712,1824.12,712,1832.453,712C1840.786,712,1849.12,712,1857.453,712C1865.786,712,1874.12,712,1884.263,715.186C1894.406,718.373,1906.358,724.745,1912.334,727.932L1918.31,731.118"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E3_F1_12" d="M1807.453,924L1811.62,924C1815.786,924,1824.12,924,1832.453,924C1840.786,924,1849.12,924,1857.453,924C1865.786,924,1874.12,924,1888.333,917.528C1902.546,911.055,1922.638,898.111,1932.684,891.639L1942.731,885.166"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F1_F2_13" d="M2217.563,808L2221.729,808C2225.896,808,2234.229,808,2241.896,808C2249.563,808,2256.563,808,2260.063,808L2263.563,808"></path><path marker-end="url(#mermaid-0619c536-f74c-40b1-a9cd-7fcb24bcfa12_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_F3_14" d="M2590.828,808L2594.995,808C2599.161,808,2607.495,808,2615.161,808C2622.828,808,2629.828,808,2633.328,808L2636.828,808"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(163, 106)" id="flowchart-A1-290" class="node default"><rect height="126" width="223.09375" y="-63" x="-111.546875" style="fill:#ffcdd2 !important" class="basic label-container"></rect><g transform="translate(-81.546875, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="163.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 rename_pic.ipynb<br>🔧 rename_images()<br>📍 lines 4-21<br>功能: 格式转换+重命名</p></span></div></foreignObject></g></g><g transform="translate(163, 294)" id="flowchart-A2-291" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 count_image.ipynb<br>🔧 count_images_in_folder()<br>📍 lines 5-15<br>功能: 图片统计</p></span></div></foreignObject></g></g><g transform="translate(560.2265625, 200)" id="flowchart-B1-292" class="node default"><rect height="150" width="334.453125" y="-75" x="-167.2265625" style="fill:#f8bbd9 !important" class="basic label-container"></rect><g transform="translate(-137.2265625, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="274.453125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 bleu_evaluation_system.py<br>🔧 TopoImageProcessor.get_image_path()<br>📍 lines 70-83<br>功能: 智能路径查找</p></span></div></foreignObject></g></g><g transform="translate(560.2265625, 400)" id="flowchart-B2-293" class="node default"><rect height="150" width="306.125" y="-75" x="-153.0625" style="" class="basic label-container"></rect><g transform="translate(-123.0625, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="246.125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 generate_data_v3.py<br>🔧 convert_to_conversation_format()<br>📍 lines 22-32<br>功能: 跨目录图片查找</p></span></div></foreignObject></g></g><g transform="translate(560.2265625, 588)" id="flowchart-B3-294" class="node default"><rect height="126" width="259.75" y="-63" x="-129.875" style="" class="basic label-container"></rect><g transform="translate(-99.875, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="199.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 generate_predictions.py<br>🔧 load_image_paths()<br>📍 lines 14-37<br>功能: 批量路径加载</p></span></div></foreignObject></g></g><g transform="translate(957.453125, 306)" id="flowchart-C1-295" class="node default"><rect height="174" width="260" y="-87" x="-130" style="fill:#e1bee7 !important" class="basic label-container"></rect><g transform="translate(-100, -72)" style="" class="label"><rect></rect><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 bleu_evaluation_system.py<br>🔧 encode_image_to_base64()<br>📍 lines 61-68<br>功能: Base64编码</p></span></div></foreignObject></g></g><g transform="translate(957.453125, 561)" id="flowchart-C2-296" class="node default"><rect height="126" width="259.75" y="-63" x="-129.875" style="" class="basic label-container"></rect><g transform="translate(-99.875, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="199.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 generate_predictions.py<br>🔧 PIL.Image.open()<br>📍 line 82<br>功能: PIL图片加载</p></span></div></foreignObject></g></g><g transform="translate(1317.453125, 406)" id="flowchart-D1-297" class="node default"><rect height="126" width="235.71875" y="-63" x="-117.859375" style="fill:#c5cae9 !important" class="basic label-container"></rect><g transform="translate(-87.859375, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="175.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 split_dataset.py<br>🔧 split_data_by_ratio()<br>📍 lines 34-53<br>功能: 80/20数据划分</p></span></div></foreignObject></g></g><g transform="translate(1317.453125, 594)" id="flowchart-D2-298" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 split_dataset.py<br>🔧 create_training_format()<br>📍 lines 76-105<br>功能: ShareGPT格式转换</p></span></div></foreignObject></g></g><g transform="translate(1317.453125, 806)" id="flowchart-D3-299" class="node default"><rect height="174" width="260" y="-87" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -72)" style="" class="label"><rect></rect><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 split_chinese_dataset.py<br>🔧 create_training_format()<br>📍 lines 122-148<br>功能: 中文数据集处理</p></span></div></foreignObject></g></g><g transform="translate(1677.453125, 500)" id="flowchart-E1-300" class="node default"><rect height="150" width="260" y="-75" x="-130" style="fill:#bbdefb !important" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 LLaMA-Factory/.../loader.py<br>🔧 load_model()<br>📍 lines 122-183<br>功能: 模型加载</p></span></div></foreignObject></g></g><g transform="translate(1677.453125, 712)" id="flowchart-E2-301" class="node default"><rect height="174" width="260" y="-87" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -72)" style="" class="label"><rect></rect><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 LLaMA-Factory/.../visual.py<br>🔧 configure_visual_model()<br>📍 lines 106-118<br>功能: 视觉模型配置</p></span></div></foreignObject></g></g><g transform="translate(1677.453125, 924)" id="flowchart-E3-302" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 LLaMA-Factory/.../patcher.py<br>🔧 patch_processor()<br>📍 lines 36-40<br>功能: 处理器补丁</p></span></div></foreignObject></g></g><g transform="translate(2062.5078125, 808)" id="flowchart-F1-303" class="node default"><rect height="150" width="310.109375" y="-75" x="-155.0546875" style="fill:#b2dfdb !important" class="basic label-container"></rect><g transform="translate(-125.0546875, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="250.109375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 generate_predictions.py<br>🔧 generate_prediction_with_model()<br>📍 lines 69-110<br>功能: 模型推理</p></span></div></foreignObject></g></g><g transform="translate(2429.1953125, 808)" id="flowchart-F2-304" class="node default"><rect height="150" width="323.265625" y="-75" x="-161.6328125" style="" class="basic label-container"></rect><g transform="translate(-131.6328125, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="263.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 bleu_evaluation_system.py<br>🔧 ModelInference.generate_response()<br>📍 lines 92-110<br>功能: 响应生成</p></span></div></foreignObject></g></g><g transform="translate(2770.265625, 808)" id="flowchart-F3-305" class="node default"><rect height="126" width="258.875" y="-63" x="-129.4375" style="" class="basic label-container"></rect><g transform="translate(-99.4375, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="198.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 test_bleu_system.py<br>🔧 test_image_processing()<br>📍 lines 159-190<br>功能: 系统测试</p></span></div></foreignObject></g></g></g></g></g></svg>