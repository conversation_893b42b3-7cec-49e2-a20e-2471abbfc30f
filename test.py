from openai import OpenAI
import time

def translate_text(text, from_lang="中文", to_lang="英文", max_retries=3):
    """
    翻译文本的函数
    
    参数:
        text (str): 需要翻译的文本
        from_lang (str): 源语言，默认"中文"
        to_lang (str): 目标语言，默认"英文"
        max_retries (int): 最大重试次数，默认3次
    
    返回:
        str: 翻译后的文本
    """
    client = OpenAI(
        base_url="https://neudm2openai.zeabur.app/v1",
        api_key="sk-T05m0OqxOgKUjErs8c231e1c02E24573A17977F5E839E91c"
    )
    
    system_prompt = f"""你是一个专业的{from_lang}到{to_lang}翻译专家。请将用户输入的{from_lang}内容翻译成{to_lang}。
    翻译要求：
    1. 保持原文的意思和语气
    2. 确保译文通顺易懂
    3. 保留专业术语的准确性
    4. 注意文化差异，适当调整表达方式
    
    请直接返回翻译结果，不需要其他解释。"""
    

    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text}
        ],
        temperature=0.2,
        max_tokens=4096
    )
    return response.choices[0].message.content.strip()

def batch_translate(texts, from_lang="中文", to_lang="英文"):
    """
    批量翻译文本
    
    参数:
        texts (list): 需要翻译的文本列表
        from_lang (str): 源语言
        to_lang (str): 目标语言
    
    返回:
        list: 翻译结果列表
    """
    results = []
    for text in texts:
        translated = translate_text(text, from_lang, to_lang)
        results.append(translated)
        time.sleep(1)  # 添加延迟避免请求过快
    return results

# 使用示例
if __name__ == "__main__":
    # 单个文本翻译示例
    chinese_text = "网络拓扑是混合型拓扑。它结合了星型拓扑和网状拓扑的特点，既有中心节点连接多个终端设备的特征，又有设备之间互联的特征。"
    print("原文:", chinese_text)
    translated = translate_text(chinese_text)
    print("译文:", translated)
    print("-" * 50)
    
    # # 批量翻译示例
    # texts_to_translate = [
    #     "这是一个星形网络拓扑。",
    #     "路由器负责数据包的转发。",
    #     "交换机连接了多个终端设备。"
    # ]
    # print("批量翻译示例:")
    # translations = batch_translate(texts_to_translate)
    # for original, translated in zip(texts_to_translate, translations):
    #     print(f"原文: {original}")
    #     print(f"译文: {translated}")
    #     print()