#!/usr/bin/env python3
"""
中文数据集划分脚本
从merged_summary_zh.json和merged_conversations.json中提取中文数据
分别生成conversation和summary两种问题类型的训练集和测试集

使用方法:
python split_chinese_dataset.py
"""

import json
import os
import random
import shutil
from pathlib import Path
from typing import List, Dict, Any
import argparse
import re

def is_chinese_text(text: str) -> bool:
    """判断文本是否主要为中文"""
    if not text:
        return False
    
    # 计算中文字符的比例
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    total_chars = len(re.findall(r'[^\s\n]', text))  # 排除空格和换行
    
    if total_chars == 0:
        return False
    
    chinese_ratio = len(chinese_chars) / total_chars
    return chinese_ratio > 0.3  # 如果中文字符超过30%，认为是中文文本

def filter_chinese_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """过滤出中文数据"""
    chinese_data = []
    
    for item in data:
        conversations = item.get("conversations", [])
        if not conversations:
            continue
        
        # 检查对话中是否包含中文
        is_chinese = False
        for conv in conversations:
            value = conv.get("value", "")
            if is_chinese_text(value):
                is_chinese = True
                break
        
        if is_chinese:
            chinese_data.append(item)
    
    return chinese_data

def load_and_filter_data(file_path: str) -> List[Dict[str, Any]]:
    """加载并过滤中文数据"""
    print(f"📖 加载数据文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✅ 原始数据: {len(data)} 个样本")
    
    # 过滤中文数据
    chinese_data = filter_chinese_data(data)
    print(f"🇨🇳 中文数据: {len(chinese_data)} 个样本")
    
    return chinese_data

def split_data_by_ratio(data: List[Dict[str, Any]], train_ratio: float = 0.8, seed: int = 42) -> tuple:
    """按比例划分数据集"""
    print(f"🔀 按 {train_ratio*100:.0f}%:{(1-train_ratio)*100:.0f}% 比例划分数据集")
    
    # 设置随机种子确保可重现性
    random.seed(seed)
    
    # 随机打乱数据
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)
    
    # 计算划分点
    split_point = int(len(shuffled_data) * train_ratio)
    
    train_data = shuffled_data[:split_point]
    test_data = shuffled_data[split_point:]
    
    print(f"✅ 训练集: {len(train_data)} 个样本")
    print(f"✅ 测试集: {len(test_data)} 个样本")
    
    return train_data, test_data

def extract_file_id_from_path(image_path: str) -> str:
    """从图像路径中提取文件ID"""
    if not image_path:
        return ""
    # 从路径中提取文件名，去掉扩展名
    filename = Path(image_path).stem
    return filename

def save_split_data(train_data: List[Dict[str, Any]], test_data: List[Dict[str, Any]], 
                   data_type: str, output_dir: str = "chinese_dataset_split"):
    """保存划分后的数据"""
    output_path = Path(output_dir) / data_type
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 保存训练集
    train_file = output_path / "train_data.json"
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    print(f"💾 {data_type}训练集已保存到: {train_file}")
    
    # 保存测试集
    test_file = output_path / "test_data.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    print(f"💾 {data_type}测试集已保存到: {test_file}")
    
    return train_file, test_file

def create_training_format(train_data: List[Dict[str, Any]], data_type: str, 
                          output_dir: str = "chinese_dataset_split"):
    """创建适合LLaMA-Factory训练的格式"""
    output_path = Path(output_dir) / data_type
    
    # 创建ShareGPT格式的训练数据
    sharegpt_format = []
    
    for item in train_data:
        # 提取图像路径
        image_path = item.get("images", "")
        
        # 转换对话格式
        conversations = item.get("conversations", [])
        
        # 创建ShareGPT格式的条目
        sharegpt_item = {
            "conversations": conversations,
            "images": image_path
        }
        
        sharegpt_format.append(sharegpt_item)
    
    # 保存ShareGPT格式的训练数据
    sharegpt_file = output_path / "train_sharegpt_format.json"
    with open(sharegpt_file, 'w', encoding='utf-8') as f:
        json.dump(sharegpt_format, f, ensure_ascii=False, indent=2)
    
    print(f"🎯 {data_type} ShareGPT格式训练数据已保存到: {sharegpt_file}")
    
    return sharegpt_file

def create_test_reference_data(test_data: List[Dict[str, Any]], data_type: str,
                              output_dir: str = "chinese_dataset_split"):
    """创建测试集的参考答案数据，用于BLEU评估"""
    output_path = Path(output_dir) / data_type
    test_ref_dir = output_path / "test_references"
    test_ref_dir.mkdir(exist_ok=True)
    
    # 为每个测试样本创建参考答案文件
    for idx, item in enumerate(test_data):
        image_path = item.get("images", "")
        file_id = extract_file_id_from_path(image_path)
        
        # 如果没有文件ID，使用索引
        if not file_id:
            file_id = f"{data_type}_{idx:04d}"
        
        conversations = item.get("conversations", [])
        
        # 提取问答对
        qa_pairs = []
        for i in range(0, len(conversations), 2):
            if i + 1 < len(conversations):
                question = conversations[i].get("value", "")
                answer = conversations[i + 1].get("value", "")
                qa_pairs.append({
                    "question": question,
                    "answer": answer
                })
        
        # 创建参考答案文件
        ref_data = {
            "entities": [],  # 可以根据需要添加实体信息
            "qa_pairs": qa_pairs
        }
        
        ref_file = test_ref_dir / f"{file_id}.json"
        with open(ref_file, 'w', encoding='utf-8') as f:
            json.dump(ref_data, f, ensure_ascii=False, indent=2)
    
    print(f"📋 {data_type}测试集参考答案已保存到: {test_ref_dir}")
    print(f"   共创建 {len(test_data)} 个参考答案文件")
    
    return test_ref_dir

def copy_test_images(test_data: List[Dict[str, Any]], data_type: str,
                    output_dir: str = "chinese_dataset_split"):
    """复制测试集对应的图像文件"""
    output_path = Path(output_dir) / data_type
    test_images_dir = output_path / "test_images"
    test_images_dir.mkdir(exist_ok=True)
    
    copied_count = 0
    missing_count = 0
    
    for item in test_data:
        image_path = item.get("images", "")
        if not image_path:
            continue
            
        # 处理相对路径和绝对路径
        if image_path.startswith("/"):
            # 绝对路径，需要转换为相对路径
            source_path = Path(image_path)
        else:
            # 相对路径
            source_path = Path(image_path)
        
        # 如果源路径不存在，尝试在data/images中查找
        if not source_path.exists():
            file_id = extract_file_id_from_path(image_path)
            # 尝试在easy和normal目录中查找
            for difficulty in ["easy", "normal"]:
                for ext in [".jpg", ".jpeg", ".png", ".gif", ".webp"]:
                    potential_path = Path(f"data/images/{difficulty}/{file_id}{ext}")
                    if potential_path.exists():
                        source_path = potential_path
                        break
                if source_path.exists():
                    break
        
        if source_path.exists():
            # 复制文件
            dest_path = test_images_dir / source_path.name
            shutil.copy2(source_path, dest_path)
            copied_count += 1
        else:
            print(f"⚠️ 未找到图像文件: {image_path}")
            missing_count += 1
    
    print(f"🖼️ {data_type}测试集图像已复制到: {test_images_dir}")
    print(f"   成功复制: {copied_count} 个文件")
    if missing_count > 0:
        print(f"   缺失文件: {missing_count} 个")
    
    return test_images_dir

def generate_summary_report(summary_stats: Dict, conversation_stats: Dict, 
                          output_dir: str = "chinese_dataset_split"):
    """生成数据集划分总结报告"""
    output_path = Path(output_dir)
    
    # 生成报告
    report = f"""# 中文数据集划分报告

## 📊 基本统计

### Summary类型数据
- **总样本数**: {summary_stats['total']}
- **训练集**: {summary_stats['train']} 个样本 ({summary_stats['train']/summary_stats['total']:.1%})
- **测试集**: {summary_stats['test']} 个样本 ({summary_stats['test']/summary_stats['total']:.1%})

### Conversation类型数据
- **总样本数**: {conversation_stats['total']}
- **训练集**: {conversation_stats['train']} 个样本 ({conversation_stats['train']/conversation_stats['total']:.1%})
- **测试集**: {conversation_stats['test']} 个样本 ({conversation_stats['test']/conversation_stats['total']:.1%})

## 📁 输出文件结构

```
chinese_dataset_split/
├── summary/
│   ├── train_data.json
│   ├── train_sharegpt_format.json
│   ├── test_data.json
│   ├── test_references/
│   └── test_images/
└── conversation/
    ├── train_data.json
    ├── train_sharegpt_format.json
    ├── test_data.json
    ├── test_references/
    └── test_images/
```

## 🎯 使用说明

### 训练模型
```bash
# 训练Summary模型
llamafactory-cli train --config summary_config.yaml --dataset chinese_dataset_split/summary/train_sharegpt_format.json

# 训练Conversation模型
llamafactory-cli train --config conversation_config.yaml --dataset chinese_dataset_split/conversation/train_sharegpt_format.json
```

### 评估模型
```bash
# 评估Summary模型
python bleu_evaluation_system.py \\
    --difficulty custom \\
    --image_dir chinese_dataset_split/summary/test_images \\
    --reference_dir chinese_dataset_split/summary/test_references

# 评估Conversation模型
python bleu_evaluation_system.py \\
    --difficulty custom \\
    --image_dir chinese_dataset_split/conversation/test_images \\
    --reference_dir chinese_dataset_split/conversation/test_references
```

## 📝 注意事项

1. 只包含中文数据，英文数据已被过滤
2. Summary数据：网络拓扑总结任务
3. Conversation数据：详细的QA对话任务
4. 随机种子设置为42，确保结果可重现
5. 两种数据类型可以分别训练不同的模型
"""

    report_file = output_path / "chinese_dataset_split_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📋 中文数据集划分报告已保存到: {report_file}")
    return report_file

def main():
    parser = argparse.ArgumentParser(description="中文数据集划分工具")
    parser.add_argument("--summary_input", default="merged_summary_zh.json", 
                       help="Summary数据文件路径")
    parser.add_argument("--conversation_input", default="Topo2text_data/merged_conversations.json", 
                       help="Conversation数据文件路径")
    parser.add_argument("--output", default="chinese_dataset_split", 
                       help="输出目录")
    parser.add_argument("--train_ratio", type=float, default=0.8, 
                       help="训练集比例 (默认: 0.8)")
    parser.add_argument("--seed", type=int, default=42, 
                       help="随机种子 (默认: 42)")
    
    args = parser.parse_args()
    
    print("🚀 开始中文数据集划分")
    print("=" * 60)
    
    # 处理Summary数据
    print("\n📝 处理Summary数据...")
    summary_data = load_and_filter_data(args.summary_input)
    summary_train, summary_test = split_data_by_ratio(summary_data, args.train_ratio, args.seed)
    
    save_split_data(summary_train, summary_test, "summary", args.output)
    create_training_format(summary_train, "summary", args.output)
    create_test_reference_data(summary_test, "summary", args.output)
    copy_test_images(summary_test, "summary", args.output)
    
    # 处理Conversation数据
    print("\n💬 处理Conversation数据...")
    conversation_data = load_and_filter_data(args.conversation_input)
    conversation_train, conversation_test = split_data_by_ratio(conversation_data, args.train_ratio, args.seed)
    
    save_split_data(conversation_train, conversation_test, "conversation", args.output)
    create_training_format(conversation_train, "conversation", args.output)
    create_test_reference_data(conversation_test, "conversation", args.output)
    copy_test_images(conversation_test, "conversation", args.output)
    
    # 生成报告
    summary_stats = {
        'total': len(summary_data),
        'train': len(summary_train),
        'test': len(summary_test)
    }
    
    conversation_stats = {
        'total': len(conversation_data),
        'train': len(conversation_train),
        'test': len(conversation_test)
    }
    
    generate_summary_report(summary_stats, conversation_stats, args.output)
    
    print("\n" + "=" * 60)
    print("🎉 中文数据集划分完成！")
    print(f"📁 输出目录: {args.output}")
    print(f"📝 Summary - 训练集: {len(summary_train)}, 测试集: {len(summary_test)}")
    print(f"💬 Conversation - 训练集: {len(conversation_train)}, 测试集: {len(conversation_test)}")

if __name__ == "__main__":
    main()
