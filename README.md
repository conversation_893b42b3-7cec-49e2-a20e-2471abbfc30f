# TOPO2TEXT

## 概述

@东北大学 Leonzy

本项目旨在开发一个能够从图像中自动识别和描述网络拓扑结构的系统。我们利用大型视觉语言模型（LVLMs），特别是 Qwen2-VL 系列，通过微调使其适应网络拓扑识别任务。

## 项目意义

本项目在自动化网络文档编制、基础设施管理、网络安全、教育等领域具有重要价值，建立一个数据benchmark。

## 模型训练

本项目使用 LoRA (Low-Rank Adaptation) 技术对 Qwen2-VL 模型进行微调。

* **LoRA 微调:** LoRA 是一种参数高效的微调方法，它通过在预训练模型的特定层中插入少量可训练参数来适应下游任务。

## 使用方法

本项目提供了一个 `train.sh` 脚本来简化模型的训练过程。

### 运行 `train.sh` 脚本

要开始模型的微调，请按照以下步骤操作：

1.  **准备数据集：**
    *   确保你的数据集已经准备好，并且符合项目的数据格式要求。
    *   你需要指定数据集的路径。

2.  **配置 `train.sh` 脚本：**

3.  **运行脚本：**
    *   在终端中，切换到项目根目录，并运行以下命令：
        ```bash
        bash train.sh
        ```
    *   如果 `train.sh` 文件没有执行权限，请先执行 `chmod +x train.sh`。

4.  **监控训练：**
    *   训练过程中的日志将保存到 `logs` 中。
    *   你可以通过查看日志来监控训练进度。




