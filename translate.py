from openai import OpenAI
import json
import os
from tqdm import tqdm
import subprocess
import math
from concurrent.futures import ProcessPoolExecutor
import multiprocessing

def translate_batch(args):
    input_dir, output_dir, start_idx, end_idx = args
    client = OpenAI(
        base_url="https://api.deepseek.com/",
        api_key="***********************************"
    )
    print("start translate")
    files = sorted([f for f in os.listdir(input_dir) if f.endswith('.json')])
    batch_files = files[start_idx:end_idx]
    exists = os.listdir(output_dir)
    for filename in tqdm(batch_files, desc=f"Process {start_idx}-{end_idx}"):
        if filename in exists:
            continue
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename)
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"translate file {filename}")
            for qa in data['qa_pairs']:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {
                            "role": "system",
                            "content": """你是一个中英文翻译专家，将用户输入的内容翻译成英文。"""
                        },
                        {
                            "role": "user",
                            "content": qa['question']
                        }
                    ],
                    temperature=0.2,
                    max_tokens=4096
                )
                qa['question'] = response.choices[0].message.content

                response = client.chat.completions.create(
                    model="deepseek-chat", 
                    messages=[
                        {
                            "role": "system",
                            "content": """你是一个中英文翻译专家，将用户输入的内容翻译成英文。"""
                        },
                        {
                            "role": "user",
                            "content": qa['answer']
                        }
                    ],
                    temperature=0.2,
                    max_tokens=4096
                )
                qa['answer'] = response.choices[0].message.content
                
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            continue
            
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

def process_directory_parallel(input_dir, output_dir, num_processes=5):
    # 获取所有json文件
    files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    total_files = len(files)
    
    # 计算每个进程处理的文件数量
    files_per_process = math.ceil(total_files / num_processes)
    
    # 创建进程参数
    process_args = []
    for i in range(num_processes):
        start_idx = i * files_per_process
        end_idx = min((i + 1) * files_per_process, total_files)
        process_args.append((input_dir, output_dir, start_idx, end_idx))
    
    # 使用进程池执行任务
    with ProcessPoolExecutor(max_workers=num_processes,mp_context=multiprocessing.get_context("fork")) as executor:
        executor.map(translate_batch, process_args)

if __name__ == "__main__":
    process_directory_parallel(
        input_dir='Topo2text_data/已修改数据/normal',
        output_dir='Topo2text_data/已修改数据/normal/英文'
    )