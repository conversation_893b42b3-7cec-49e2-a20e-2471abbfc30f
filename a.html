<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1215.59375 3830" style="max-width: 1215.59375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe"><style>#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .error-icon{fill:#a44141;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .error-text{fill:#ddd;stroke:#ddd;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-thickness-normal{stroke-width:1px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-thickness-thick{stroke-width:3.5px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-pattern-solid{stroke-dasharray:0;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .marker.cross{stroke:lightgrey;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe p{margin:0;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster-label text{fill:#F9FFFE;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster-label span{color:#F9FFFE;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster-label span p{background-color:transparent;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .label text,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe span{fill:#ccc;color:#ccc;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node rect,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node circle,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node ellipse,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node polygon,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .rough-node .label text,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node .label text,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .image-shape .label,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .icon-shape .label{text-anchor:middle;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .rough-node .label,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node .label,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .image-shape .label,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .icon-shape .label{text-align:center;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .node.clickable{cursor:pointer;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .arrowheadPath{fill:lightgrey;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster text{fill:#F9FFFE;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .cluster span{color:#F9FFFE;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe rect.text{fill:none;stroke-width:0;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .icon-shape,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .icon-shape p,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .icon-shape rect,#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M678.223,62L678.223,66.167C678.223,70.333,678.223,78.667,678.223,86.333C678.223,94,678.223,101,678.223,104.5L678.223,108"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M612.292,190L601.867,196.167C591.442,202.333,570.592,214.667,560.167,226.333C549.742,238,549.742,249,549.742,254.5L549.742,260"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_2" d="M744.153,190L754.578,196.167C765.003,202.333,785.853,214.667,796.278,228.333C806.703,242,806.703,257,806.703,264.5L806.703,272"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_3" d="M549.742,366L549.742,370.167C549.742,374.333,549.742,382.667,556.212,390.661C562.682,398.654,575.623,406.309,582.093,410.136L588.563,413.963"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_4" d="M806.703,354L806.703,360.167C806.703,366.333,806.703,378.667,800.233,388.661C793.763,398.654,780.823,406.309,774.353,410.136L767.883,413.963"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_5" d="M678.223,518L678.223,522.167C678.223,526.333,678.223,534.667,678.223,542.333C678.223,550,678.223,557,678.223,560.5L678.223,564"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_6" d="M579.824,622.323L527.729,630.436C475.633,638.549,371.441,654.774,319.346,666.387C267.25,678,267.25,685,267.25,688.5L267.25,692"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_7" d="M594.664,646L585.737,650.167C576.81,654.333,558.956,662.667,550.029,674.333C541.102,686,541.102,701,541.102,708.5L541.102,716"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_I_8" d="M761.781,646L770.708,650.167C779.635,654.333,797.489,662.667,806.417,674.333C815.344,686,815.344,701,815.344,708.5L815.344,716"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_J_9" d="M776.621,622.282L828.904,630.401C881.188,638.521,985.754,654.761,1038.037,670.38C1090.32,686,1090.32,701,1090.32,708.5L1090.32,716"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_K_10" d="M267.25,798L267.25,802.167C267.25,806.333,267.25,814.667,284.378,823.587C301.507,832.507,335.764,842.014,352.892,846.768L370.021,851.521"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_K_11" d="M541.102,774L541.102,782.167C541.102,790.333,541.102,806.667,541.102,818.333C541.102,830,541.102,837,541.102,840.5L541.102,844"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_K_12" d="M815.344,774L815.344,782.167C815.344,790.333,815.344,806.667,798.15,819.598C780.957,832.53,746.57,842.059,729.376,846.824L712.183,851.589"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_13" d="M541.102,950L541.102,954.167C541.102,958.333,541.102,966.667,541.102,974.333C541.102,982,541.102,989,541.102,992.5L541.102,996"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_14" d="M541.102,1150L541.102,1154.167C541.102,1158.333,541.102,1166.667,541.102,1174.333C541.102,1182,541.102,1189,541.102,1192.5L541.102,1196"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_15" d="M541.102,1350L541.102,1354.167C541.102,1358.333,541.102,1366.667,541.102,1374.333C541.102,1382,541.102,1389,541.102,1392.5L541.102,1396"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_16" d="M451.996,1502L441.222,1508.167C430.448,1514.333,408.9,1526.667,398.126,1538.333C387.352,1550,387.352,1561,387.352,1566.5L387.352,1572"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_P_17" d="M630.433,1502L641.235,1508.167C652.036,1514.333,673.639,1526.667,684.441,1538.333C695.242,1550,695.242,1561,695.242,1566.5L695.242,1572"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_Q_18" d="M387.352,1678L387.352,1682.167C387.352,1686.333,387.352,1694.667,387.352,1702.333C387.352,1710,387.352,1717,387.352,1720.5L387.352,1724"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_19" d="M281.883,1820.147L266.993,1825.956C252.104,1831.765,222.326,1843.382,207.436,1852.691C192.547,1862,192.547,1869,192.547,1872.5L192.547,1876"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_S_20" d="M492.82,1820.147L507.71,1825.956C522.599,1831.765,552.378,1843.382,567.267,1852.691C582.156,1862,582.156,1869,582.156,1872.5L582.156,1876"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_T_21" d="M192.547,1958L192.547,1962.167C192.547,1966.333,192.547,1974.667,192.547,1982.333C192.547,1990,192.547,1997,192.547,2000.5L192.547,2004"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_U_22" d="M582.156,1958L582.156,1962.167C582.156,1966.333,582.156,1974.667,582.156,1982.333C582.156,1990,582.156,1997,582.156,2000.5L582.156,2004"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_V_23" d="M192.547,2110L192.547,2114.167C192.547,2118.333,192.547,2126.667,192.547,2136.333C192.547,2146,192.547,2157,192.547,2162.5L192.547,2168"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_W_24" d="M582.156,2110L582.156,2114.167C582.156,2118.333,582.156,2126.667,582.156,2134.333C582.156,2142,582.156,2149,582.156,2152.5L582.156,2156"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_X_25" d="M192.547,2250L192.547,2256.167C192.547,2262.333,192.547,2274.667,192.547,2284.333C192.547,2294,192.547,2301,192.547,2304.5L192.547,2308"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_Y_26" d="M582.156,2262L582.156,2266.167C582.156,2270.333,582.156,2278.667,582.156,2288.333C582.156,2298,582.156,2309,582.156,2314.5L582.156,2320"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Z_27" d="M192.547,2438L192.547,2442.167C192.547,2446.333,192.547,2454.667,201.163,2462.726C209.779,2470.784,227.011,2478.569,235.628,2482.461L244.244,2486.353"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_28" d="M582.156,2426L582.156,2432.167C582.156,2438.333,582.156,2450.667,573.54,2460.726C564.924,2470.784,547.692,2478.569,539.075,2482.461L530.459,2486.353"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_29" d="M387.352,2614L387.352,2618.167C387.352,2622.333,387.352,2630.667,387.352,2638.333C387.352,2646,387.352,2653,387.352,2656.5L387.352,2660"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_30" d="M387.352,2790L387.352,2794.167C387.352,2798.333,387.352,2806.667,387.352,2814.333C387.352,2822,387.352,2829,387.352,2832.5L387.352,2836"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_31" d="M387.352,2942L387.352,2946.167C387.352,2950.333,387.352,2958.667,387.352,2966.333C387.352,2974,387.352,2981,387.352,2984.5L387.352,2988"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_32" d="M387.352,3118L387.352,3122.167C387.352,3126.333,387.352,3134.667,387.352,3142.333C387.352,3150,387.352,3157,387.352,3160.5L387.352,3164"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_EE_33" d="M387.352,3294L387.352,3298.167C387.352,3302.333,387.352,3310.667,387.352,3318.333C387.352,3326,387.352,3333,387.352,3336.5L387.352,3340"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_FF_34" d="M387.352,3470L387.352,3474.167C387.352,3478.333,387.352,3486.667,387.352,3494.333C387.352,3502,387.352,3509,387.352,3512.5L387.352,3516"></path><path marker-end="url(#mermaid-034612d6-da71-48c0-9cf0-b71671aaeffe_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_GG_35" d="M387.352,3646L387.352,3650.167C387.352,3654.333,387.352,3662.667,387.352,3670.333C387.352,3678,387.352,3685,387.352,3688.5L387.352,3692"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(549.7421875, 227)" class="edgeLabel"><g transform="translate(-30.4453125, -12)" class="label"><foreignObject height="24" width="60.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>JFIF格式</p></span></div></foreignObject></g></g><g transform="translate(806.703125, 227)" class="edgeLabel"><g transform="translate(-32.0078125, -12)" class="label"><foreignObject height="24" width="64.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>其他格式</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(387.3515625, 1539)" class="edgeLabel"><g transform="translate(-16.0078125, -12)" class="label"><foreignObject height="24" width="32.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>成功</p></span></div></foreignObject></g></g><g transform="translate(695.2421875, 1539)" class="edgeLabel"><g transform="translate(-16.0078125, -12)" class="label"><foreignObject height="24" width="32.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>失败</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(678.22265625, 35)" id="flowchart-A-168" class="node default"><rect height="54" width="180.796875" y="-27" x="-90.3984375" style="" class="basic label-container"></rect><g transform="translate(-60.3984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="120.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔵 原始图片数据</p></span></div></foreignObject></g></g><g transform="translate(678.22265625, 151)" id="flowchart-B-169" class="node default"><rect height="78" width="188.796875" y="-39" x="-94.3984375" style="" class="basic label-container"></rect><g transform="translate(-64.3984375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟡 图片格式检查<br>rename_pic.ipynb</p></span></div></foreignObject></g></g><g transform="translate(549.7421875, 315)" id="flowchart-C-171" class="node default"><rect height="102" width="225.125" y="-51" x="-112.5625" style="" class="basic label-container"></rect><g transform="translate(-82.5625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="165.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟠 格式转换 JFIF→JPG<br>rename_pic.ipynb<br>lines 14-15</p></span></div></foreignObject></g></g><g transform="translate(806.703125, 315)" id="flowchart-D-173" class="node default"><rect height="78" width="188.796875" y="-39" x="-94.3984375" style="" class="basic label-container"></rect><g transform="translate(-64.3984375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟢 保持原格式<br>rename_pic.ipynb</p></span></div></foreignObject></g></g><g transform="translate(678.22265625, 467)" id="flowchart-E-175" class="node default"><rect height="102" width="188.796875" y="-51" x="-94.3984375" style="" class="basic label-container"></rect><g transform="translate(-64.3984375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="128.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔴 图片重命名<br>rename_pic.ipynb<br>lines 16-20</p></span></div></foreignObject></g></g><g transform="translate(678.22265625, 607)" id="flowchart-F-179" class="node default"><rect height="78" width="196.796875" y="-39" x="-98.3984375" style="" class="basic label-container"></rect><g transform="translate(-68.3984375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚫ 按难度分类存储<br>手动分类到目录</p></span></div></foreignObject></g></g><g transform="translate(267.25, 747)" id="flowchart-G-181" class="node default"><rect height="102" width="214.625" y="-51" x="-107.3125" style="" class="basic label-container"></rect><g transform="translate(-77.3125, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="154.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟦 data/images/easy<br>count_image.ipynb<br>lines 19-23</p></span></div></foreignObject></g></g><g transform="translate(541.1015625, 747)" id="flowchart-H-183" class="node default"><rect height="54" width="233.078125" y="-27" x="-116.5390625" style="" class="basic label-container"></rect><g transform="translate(-86.5390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="173.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟦 data/images/normal</p></span></div></foreignObject></g></g><g transform="translate(815.34375, 747)" id="flowchart-I-185" class="node default"><rect height="54" width="215.40625" y="-27" x="-107.703125" style="" class="basic label-container"></rect><g transform="translate(-77.703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="155.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟦 data/images/hard</p></span></div></foreignObject></g></g><g transform="translate(1090.3203125, 747)" id="flowchart-J-187" class="node default"><rect height="54" width="234.546875" y="-27" x="-117.2734375" style="" class="basic label-container"></rect><g transform="translate(-87.2734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="174.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟦 data/images/discard</p></span></div></foreignObject></g></g><g transform="translate(541.1015625, 899)" id="flowchart-K-189" class="node default"><rect height="102" width="334.453125" y="-51" x="-167.2265625" style="" class="basic label-container"></rect><g transform="translate(-137.2265625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="274.453125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟨 图片路径查找系统<br>bleu_evaluation_system.py<br>TopoImageProcessor.get_image_path()</p></span></div></foreignObject></g></g><g transform="translate(541.1015625, 1075)" id="flowchart-L-195" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟧 多格式支持<br>bleu_evaluation_system.py lines 75-76<br>generate_predictions.py lines 29</p></span></div></foreignObject></g></g><g transform="translate(541.1015625, 1275)" id="flowchart-M-197" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟩 智能路径匹配<br>generate_data_v3.py lines 22-32<br>split_dataset.py lines 176-182</p></span></div></foreignObject></g></g><g transform="translate(541.1015625, 1451)" id="flowchart-N-199" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟪 图片加载验证<br>bleu_evaluation_system.py lines 77-80</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 1627)" id="flowchart-O-201" class="node default"><rect height="102" width="255.78125" y="-51" x="-127.890625" style="" class="basic label-container"></rect><g transform="translate(-97.890625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="195.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔺 Base64编码<br>bleu_evaluation_system.py<br>encode_image_to_base64()</p></span></div></foreignObject></g></g><g transform="translate(695.2421875, 1627)" id="flowchart-P-203" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⭕ 错误处理和日志<br>bleu_evaluation_system.py lines 67-68</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 1779)" id="flowchart-Q-205" class="node default"><rect height="102" width="210.9375" y="-51" x="-105.46875" style="" class="basic label-container"></rect><g transform="translate(-75.46875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="150.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔶 数据集划分<br>split_dataset.py<br>split_data_by_ratio()</p></span></div></foreignObject></g></g><g transform="translate(192.546875, 1919)" id="flowchart-R-207" class="node default"><rect height="78" width="258.015625" y="-39" x="-129.0078125" style="" class="basic label-container"></rect><g transform="translate(-99.0078125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="198.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📗 80% 训练集<br>split_dataset.py lines 46-48</p></span></div></foreignObject></g></g><g transform="translate(582.15625, 1919)" id="flowchart-S-209" class="node default"><rect height="78" width="258.015625" y="-39" x="-129.0078125" style="" class="basic label-container"></rect><g transform="translate(-99.0078125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="198.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📘 20% 测试集<br>split_dataset.py lines 46-49</p></span></div></foreignObject></g></g><g transform="translate(192.546875, 2059)" id="flowchart-T-211" class="node default"><rect height="102" width="239.234375" y="-51" x="-119.6171875" style="" class="basic label-container"></rect><g transform="translate(-89.6171875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="179.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📙 ShareGPT格式转换<br>split_dataset.py<br>create_training_format()</p></span></div></foreignObject></g></g><g transform="translate(582.15625, 2059)" id="flowchart-U-213" class="node default"><rect height="102" width="273.15625" y="-51" x="-136.578125" style="" class="basic label-container"></rect><g transform="translate(-106.578125, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="213.15625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📕 测试参考数据<br>split_dataset.py<br>create_test_reference_data()</p></span></div></foreignObject></g></g><g transform="translate(192.546875, 2211)" id="flowchart-V-215" class="node default"><rect height="78" width="258.015625" y="-39" x="-129.0078125" style="" class="basic label-container"></rect><g transform="translate(-99.0078125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="198.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔳 训练数据准备<br>split_dataset.py lines 91-96</p></span></div></foreignObject></g></g><g transform="translate(582.15625, 2211)" id="flowchart-W-217" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔲 评估数据准备<br>split_dataset.py lines 130-140</p></span></div></foreignObject></g></g><g transform="translate(192.546875, 2375)" id="flowchart-X-219" class="node default"><rect height="126" width="369.09375" y="-63" x="-184.546875" style="" class="basic label-container"></rect><g transform="translate(-154.546875, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="309.09375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⬛ 模型训练输入<br>LLaMA-Factory/src/llamafactory/model/loader.py<br>load_model()</p></span></div></foreignObject></g></g><g transform="translate(582.15625, 2375)" id="flowchart-Y-221" class="node default"><rect height="102" width="310.125" y="-51" x="-155.0625" style="" class="basic label-container"></rect><g transform="translate(-125.0625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="250.125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⬜ BLEU评估输入<br>generate_predictions.py<br>generate_prediction_with_model()</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 2551)" id="flowchart-Z-223" class="node default"><rect height="126" width="334.734375" y="-63" x="-167.3671875" style="" class="basic label-container"></rect><g transform="translate(-137.3671875, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="274.734375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟫 PIL图片加载<br>generate_predictions.py line 82<br>LLaMA-Factory/src/llamafactory/api/chat.py</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 2727)" id="flowchart-AA-227" class="node default"><rect height="126" width="378.796875" y="-63" x="-189.3984375" style="" class="basic label-container"></rect><g transform="translate(-159.3984375, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="318.796875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔘 图片预处理<br>generate_predictions.py lines 87-91<br>LLaMA-Factory/src/llamafactory/model/patcher.py</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 2891)" id="flowchart-BB-229" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔴 RGB格式转换<br>generate_predictions.py line 82</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 3055)" id="flowchart-CC-231" class="node default"><rect height="126" width="399.328125" y="-63" x="-199.6640625" style="" class="basic label-container"></rect><g transform="translate(-169.6640625, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="339.328125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟡 模型推理<br>generate_predictions.py lines 95-102<br>LLaMA-Factory/src/llamafactory/chat/vllm_engine.py</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 3231)" id="flowchart-DD-233" class="node default"><rect height="126" width="458.25" y="-63" x="-229.125" style="" class="basic label-container"></rect><g transform="translate(-199.125, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="398.25"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟢 Qwen2.5-VL模型<br>LLaMA-Factory/src/llamafactory/model/model_utils/visual.py<br>configure_visual_model()</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 3407)" id="flowchart-EE-235" class="node default"><rect height="126" width="458.25" y="-63" x="-229.125" style="" class="basic label-container"></rect><g transform="translate(-199.125, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="398.25"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔵 视觉特征提取<br>LLaMA-Factory/src/llamafactory/model/model_utils/visual.py<br>lines 97-98</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 3583)" id="flowchart-FF-237" class="node default"><rect height="126" width="458.25" y="-63" x="-229.125" style="" class="basic label-container"></rect><g transform="translate(-199.125, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="398.25"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟠 多模态融合<br>LLaMA-Factory/src/llamafactory/model/model_utils/visual.py<br>autocast_projector_dtype()</p></span></div></foreignObject></g></g><g transform="translate(387.3515625, 3759)" id="flowchart-GG-239" class="node default"><rect height="126" width="323.265625" y="-63" x="-161.6328125" style="" class="basic label-container"></rect><g transform="translate(-131.6328125, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="263.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🟣 文本生成<br>generate_predictions.py line 105<br>bleu_evaluation_system.py ModelInference.generate_response()</p></span></div></foreignObject></g></g></g></g></g></svg>