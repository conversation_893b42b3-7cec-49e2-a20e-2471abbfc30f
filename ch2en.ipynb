{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 英文翻译为中文\n", "将生成的英文数据Summary部分翻译为中文，经测试直接生成中文数据受编码方式限制对长度控制不准确\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["请简要总结网络拓扑，字数限制如下：少于10个节点时不超过100个字，10到15个节点时不超过200个字，超过15个节点时不超过300个字。总结应包括以下内容：\n", "\n", "*   **整体架构：** 星型、总线型、环型、网状、树型或混合型。\n", "*   **区域/设备描述：** 描述每个主要区域或设备（例如，服务器、路由器、交换机、客户端等），包括其数量、类型和主要用途。\n", "*   **连接方式：** 描述区域/设备之间的连接类型（例如，有线、无线、光纤）和连接关系（连接到哪些节点）。\n", "*   **数据流：** 概述数据或控制信息在网络中的流动方式。\n", "*   **网络目标：** 总结网络架构的主要设计目标和功能，例如数据传输、资源共享、安全访问等。\n", "\n", "请使用以下结构进行总结：\n", "\n", "该网络是一个[整体架构]拓扑。它由多个互联的区域和设备组成。首先，[区域/设备名称1]包括[数量][设备类型]，通过[连接方式]连接。该区域的主要目的是[区域/设备1的目的]。它通过[连接类型]连接到[区域/设备名称2]，促进数据和控制流动。[区域/设备名称2]由[数量][设备类型]组成，作为[区域/设备2的目的]。此外，[区域/设备名称2]还连接到其他区域/设备，如[区域/设备名称3]，形成整体网络结构。区域/设备之间的连接还包括[连接类型，例如防火墙、VPN连接]，提供[连接的目的]。该网络旨在实现[整体网络目的，例如高效的数据处理、安全的远程访问]。\n", "该网络是一个混合型拓扑。它由多个互联的区域和设备组成。首先，“Server Hotel”包括多个服务器和防火墙，通过局域网连接。该区域的主要目的是客户托管。它通过广域网连接到Portimão，促进数据和控制流动。Portimão由路由器和交换机组成，作为中央枢纽。此外，Portimão还连接到其他区域，如Grindavik、'Agrah'和Vil'gort，形成整体网络结构。区域之间的连接包括防火墙，提供安全访问。该网络旨在实现高效的数据传输和安全的客户访问。\n", "Processed n0001.json in 11.56 seconds.\n"]}], "source": ["import os\n", "import json\n", "import time\n", "from openai import OpenAI\n", "\n", "# 设置API基础信息\n", "api_base = \"https://neudm.zeabur.app/v1\"\n", "api_key = \"sk-T05m0OqxOgKUjErs8c231e1c02E24573A17977F5E839E91c\"  \n", "input_folder = \"data/result/en_unchecked/normal\"  \n", "output_folder = \"data/ch_test\"  \n", "\n", "os.makedirs(output_folder, exist_ok=True) \n", "\n", "# 获取输入文件夹下的所有JSON文件\n", "json_files = [f for f in os.listdir(input_folder) if f.endswith('.json')]\n", "\n", "client = OpenAI(api_key=api_key, base_url=api_base)\n", "\n", "# 遍历每个JSON文件\n", "for json_file in json_files:\n", "    json_path = os.path.join(input_folder, json_file)  \n", "\n", "    with open(json_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "\n", "    second_part = data.get(\"qa_pairs\", [])\n", "    if len(second_part) > 4:  \n", "        fifth_section = second_part[4]  \n", "        question = fifth_section.get(\"question\", \"\")\n", "        answer = fifth_section.get(\"answer\", \"\")\n", "\n", "        start_time = time.time()\n", "\n", "        combined_message = (\n", "            f\"请将以下内容翻译成中文，如果网元名称中带有英文，不翻译名称的英文：\\n\"\n", "            f\"问题：{question}\\n\"\n", "            f\"答案：{answer}\"\n", "        )\n", "\n", "        completion = client.chat.completions.create(\n", "            model=\"gpt-4o-mini\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"你是一个专业的翻译助手。\"},\n", "                {\"role\": \"user\", \"content\": combined_message},\n", "            ],\n", "            temperature=0.2,\n", "            max_tokens=4096,\n", "        )\n", "\n", "        translated_text = completion.choices[0].message.content.strip()\n", "\n", "        # 分割翻译结果，假设格式是“问题：<翻译后的问题> 答案：<翻译后的答案>”\n", "        if translated_text:\n", "            translated_question, translated_answer = translated_text.split(\"答案：\")\n", "            translated_question = translated_question.replace(\"问题：\", \"\").strip()\n", "            translated_answer = translated_answer.strip()\n", "\n", "            print(translated_question)\n", "            print(translated_answer)\n", "\n", "            # 更新Summary的question和answer\n", "            fifth_section[\"question\"] = translated_question\n", "            fifth_section[\"answer\"] = translated_answer\n", "\n", "        output_json_path = os.path.join(output_folder, json_file)\n", "        with open(output_json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(data, f, ensure_ascii=False, indent=4)\n", "\n", "        duration = time.time() - start_time\n", "        print(f\"Processed {json_file} in {duration:.2f} seconds.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}