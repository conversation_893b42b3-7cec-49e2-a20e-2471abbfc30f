import json
import os
from tqdm import tqdm

### 提取前4个QA对

def convert_to_conversation_format(input_file, image_base_dir):
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取文件名(不含扩展名)用于查找对应图片
    file_name = os.path.splitext(os.path.basename(input_file))[0]
    
    # 可能的子目录
    sub_dirs = ["easy", "normal", "hard"]  # 空字符串表示根目录
    
    # 构建可能的图片路径
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_path = None
    
    # 在所有可能的子目录中查找图片
    for sub_dir in sub_dirs:
        if image_path:
            break
            
        search_dir = os.path.join(image_base_dir, sub_dir)
        
        for ext in image_extensions:
            potential_path = os.path.join(search_dir, file_name + ext)
            if os.path.exists(potential_path):
                image_path = potential_path
                break
    
    if not image_path:
        return None

    # 只取前4个QA对
    qa_pairs = data["qa_pairs"][:4]
    
    # 构建会话数据
    conversations = []
    for qa in qa_pairs:
        conversation_data = {
            "conversations": [{
                "from": "human",
                "value": qa["question"]
            },
            {
                "from": "gpt",
                "value": qa["answer"]
            }],
            "images": image_path
        }
        conversations.append(conversation_data)
    
    return conversations

def merge_json_files():
    data_dirs = [
        "Topo2text_data/已修改数据/easy/中文",
        "Topo2text_data/已修改数据/easy/英文",
        "Topo2text_data/已修改数据/normal中英/中文",
        "Topo2text_data/已修改数据/normal中英/英文"
    ]
    
    image_base_dir = "data/images"
    output_file = "Topo2text_data/merged_conversations.json"
    
    all_conversations = []
    
    for data_dir in tqdm(data_dirs, desc="Processing data directories"):
        for filename in os.listdir(data_dir):
            if filename.endswith(".json"):
                input_file = os.path.join(data_dir, filename)
                conversation_data = convert_to_conversation_format(input_file, image_base_dir)
                if conversation_data:
                    all_conversations.extend(conversation_data)
    
    # 写入合并后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_conversations, f, ensure_ascii=False, indent=2)
        
    print(f"已合并所有数据到: {output_file}")

if __name__ == "__main__":
    merge_json_files()