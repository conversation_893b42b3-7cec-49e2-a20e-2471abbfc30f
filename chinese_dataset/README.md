# 中文数据集划分报告

## 📊 基本统计

### Summary类型数据（网络拓扑总结）
- **总样本数**: 1665
- **训练集**: 1332 个样本 (80.0%)
- **测试集**: 333 个样本 (20.0%)

### Conversation类型数据（详细QA对话）
- **总样本数**: 5256
- **训练集**: 4204 个样本 (80.0%)
- **测试集**: 1052 个样本 (20.0%)

## 📁 输出文件结构

```
chinese_dataset/
├── summary/
│   ├── train.json          # 训练数据
│   ├── test.json           # 测试数据
│   └── test_images/        # 测试集图像
└── conversation/
    ├── train.json          # 训练数据
    ├── test.json           # 测试数据
    └── test_images/        # 测试集图像
```

## 🎯 使用说明

### 训练模型
```bash
# 训练Summary模型（网络拓扑总结）
llamafactory-cli train --config summary_config.yaml --dataset chinese_dataset/summary/train.json

# 训练Conversation模型（详细QA对话）
llamafactory-cli train --config conversation_config.yaml --dataset chinese_dataset/conversation/train.json
```

### 评估模型
使用test.json中的数据进行评估，图像文件在对应的test_images目录中。

## 📝 数据特点

### Summary数据
- **任务类型**: 网络拓扑总结
- **问题格式**: 要求按照特定结构总结网络拓扑
- **回答格式**: 结构化的网络描述

### Conversation数据  
- **任务类型**: 详细的技术问答
- **问题格式**: 关于网络节点、连接、架构等的具体问题
- **回答格式**: 详细的技术解答

## 📝 注意事项

1. 只包含中文数据，英文数据已被过滤
2. 数据格式为标准的conversations格式，可直接用于LLaMA-Factory训练
3. 随机种子设置为42，确保结果可重现
4. 测试集图像已复制到对应目录，便于评估使用
