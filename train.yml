model_name_or_path: /share/model/Qwen2.5-VL-7B-Instruct/
adapter_name_or_path: /share/topo_lab_res/Qwen2.5-VL-7B-Instruct/lora/train_test_con_2025-06-29-09-28-24
#/share/topo_lab_res/Qwen2.5-VL-7B-Instruct/lora/train_test_sum_2025-06-29-10-41-49

### method
stage: sft
do_predict: true
finetuning_type: lora

### dataset
eval_dataset: con
template: qwen2_vl
cutoff_len: 2048
max_samples: 50
overwrite_cache: true
preprocessing_num_workers: 16

### output
output_dir: saves/Qwen2.5-VL-7B/lora/predict
overwrite_output_dir: true

### eval
per_device_eval_batch_size: 2
predict_with_generate: true
ddp_timeout: 180000000