{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import base64\n", "import json\n", "import os\n", "import random\n", "from openai import OpenAI\n", "from pydantic import BaseModel\n", "from typing import List, Dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义图像编码函数\n", "\n", "- 4o 系列：请使用 Base64 编码将图片转换为字符串格式。\n", "- QwenVL 系列：可以直接发送原始图片数据，无需进行编码。\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def encode_image(image_path):\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义 QA 对\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class QA_Pair(BaseModel):\n", "    question: str\n", "    answer: str"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 类定义：`Cmanager`\n", "\n", "`Cmanager`类是一个用于管理网络拓扑图像和相关操作的类。它提供了初始化客户端、设置拓扑图像路径、提取实体、构建问答对以及保存数据到 JSON 文件的功能。\n", "属性：\n", "\n", "- `client`: 用于与 API 通信的客户端对象。\n", "- `topology_image_path`: 存储拓扑图像文件路径的字符串。\n", "  方法：\n", "\n", "1. `__init__(self, api_base: str, api_key: str)`：\n", "   - 初始化方法，接收两个参数：`api_base`（API 的基础 URL）和`api_key`（API 的密钥）。\n", "   - 创建客户端实例并存储在`self.client`中。\n", "   - 初始化`topology_image_path`为空字符串。\n", "2. `get_client(self, base_url, api_key)`：\n", "   - 私有方法，用于创建并返回一个`OpenAI`客户端实例。\n", "   - 参数：`base_url`和`api_key`。\n", "3. `set_topology_image(self, image_path: str)`：\n", "   - 设置拓扑图像文件路径。\n", "   - 参数：`image_path`（图像文件的路径）。\n", "4. `extract_entities(self) -> List[str]`：\n", "   - 提取拓扑图像中的网络实体。\n", "   - 检查`topology_image_path`是否已设置，如果没有，则抛出`ValueError`。\n", "   - 将图像编码为 base64 格式，并构建系统内容字符串。\n", "   - 使用`gpt-4o-mini`模型创建聊天完成请求，提取实体并返回实体列表。\n", "5. `build_qa_pairs(self, entities: List[str], num_pairs: int = 10) -> List[Dict[str, str]]`：\n", "   - 基于提取的实体构建问答对。\n", "   - 参数：`entities`（实体列表），`num_pairs`（要生成的问答对数量，默认为 10）。\n", "   - 随机选择实体和问题模板，构建问题并使用`gpt-4o-mini`模型生成答案。\n", "   - 返回包含问题和答案的字典列表。\n", "6. `save_to_json(self, entities: List[str], qa_pairs: List[Dict[str, str]], filename: str)`：\n", "   - 将实体和问答对保存为 JSON 文件。\n", "   - 参数：`entities`（实体列表），`qa_pairs`（问答对列表），`filename`（要保存的文件名）。\n", "\n", "##### 注意事项：\n", "\n", "1. **图像路径设置**：在调用`extract_entities`和`build_qa_pairs`方法之前，必须通过`set_topology_image`方法设置拓扑图像路径。\n", "2. **图像编码**：`extract_entities`和`build_qa_pairs`方法中使用了`encode_image`函数对图像进行 base64 编码，确保该函数已正确实现。\n", "3. **问答对生成**：`build_qa_pairs`方法默认生成 10 个问答对，可以根据需要调整`num_pairs`参数。\n", "4. **JSON 文件保存**：`save_to_json`方法将实体和问答对保存为 JSON 文件，确保指定的`filename`路径可写。\n", "5. **随机性**：`build_qa_pairs`方法中使用了随机选择实体和问题模板，这可能导致每次生成的问答对不同。\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class Cmanager:\n", "    def __init__(self, api_base: str, api_key: str):\n", "        self.client = self.get_client(api_base, api_key)\n", "        self.topology_image_path = \"\"\n", "\n", "    @staticmethod\n", "    def get_client(base_url: str, api_key: str) -> OpenAI:\n", "        \"\"\"Initialize and return the API client.\"\"\"\n", "        return OpenAI(base_url=base_url, api_key=api_key)\n", "\n", "    def set_topology_image(self, image_path: str):\n", "        \"\"\"Set the path for the topology image.\"\"\"\n", "        self.topology_image_path = image_path\n", "\n", "    # STEP 1: Extract entities from the topology image\n", "    def extract_entities(self) -> List[str]:\n", "        \"\"\"Extract named core network elements from the topology image.\"\"\"\n", "        if not self.topology_image_path:\n", "            raise ValueError(\"Topology image path is not set.\")\n", "\n", "        base64_image = encode_image(self.topology_image_path)\n", "        content_system = (\n", "            \"You are a network topology entity extraction expert specialized in identifying named key network elements.\"\n", "            \"You will receive an image depicting a network topology.\"\n", "            \"Your task is to identify **named** core network elements from the image.\"\n", "            \"Extract a maximum of eight (**eight (8) most crucial named core network elements**) of the core network elements from the named ones.\"\n", "            \"If there are less than eight named core network elements, output all of them.\"\n", "            \"If the image does not contain at least one named core network element, output an empty list: `[]`.\"\n", "            \"1. Core Network Elements:\"\n", "            \"  - Core network elements are essential components like routers, switches, servers, firewalls, etc., that have a distinct name in the image.\"\n", "            \"2. Output Format:\"\n", "            \"  - Output the core network element **names** (not the types) as a list of strings. Do not use any other format.\"\n", "            \"  - Do not include any explanations, analysis, or additional information.\"\n", "            f\"  - Example output for an image with named elements: ['R1', 'SW1', 'ServerA']\"\n", "            f\"  - Example output for an image with no named elements: []\"\n", "            \"3. Important Notes:\"\n", "            \"  - **Focus solely on named entities. Unnamed elements should be ignored.**\"\n", "            \"  - Extract a **maximum of eight** core network element names(different names).\"\n", "            \"  - Output the extracted names **exactly as they appear** in the image.\"\n", "            \"  - Ensure the output is a valid list.\"\n", "        )\n", "\n", "        completion = self.client.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": content_system},\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\"type\": \"text\", \"text\": \"Topology Image\"},\n", "                        {\n", "                            \"type\": \"image_url\",\n", "                            \"image_url\": {\n", "                                \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                            },\n", "                        },\n", "                    ],\n", "                },\n", "            ],\n", "            temperature=0.2,\n", "        )\n", "\n", "        response_content = completion.choices[0].message.content.strip()\n", "        try:\n", "            # Parse the response content to extract entities\n", "            list_str = response_content[2:-2]\n", "            entities = [entity.strip(\"'\") for entity in list_str.split(\", \")]\n", "            if not all(isinstance(entity, str) for entity in entities):\n", "                raise ValueError(\"Entities are not in the correct format.\")\n", "            return entities\n", "        except Exception as e:\n", "            print(f\"Error processing entities: {e}\")\n", "            return []\n", "\n", "    # STEP 2: Generate QA pairs based on extracted entities\n", "    def build_qa_pairs(\n", "        self, entities: List[str], num_pairs: int = 5\n", "    ) -> List[Dict[str, str]]:\n", "        \"\"\"Generate question-answer pairs based on the provided entities.\"\"\"\n", "        qa_pairs = []\n", "        base64_image = encode_image(self.topology_image_path)\n", "\n", "        question_templates = [\n", "            \"Please describe the overall network topology type. Is it a star, bus, ring, mesh, tree, or hybrid topology? If it is a hybrid topology, please specify the combination.\",\n", "            \"Please specify the total number of nodes in the network topology diagram. Subsequently, based on this total number of nodes, list the major nodes in the network topology, including their specific names or identifiers, and briefly describe the function of each major node (if the major nodes do not have names, assign names to them based on the topology diagram).\",\n", "            \"Please describe in detail the connections between the major nodes, including the connection type (e.g., wired, wireless, fiber optic) and the connection relationships (e.g., one-to-one, one-to-many, many-to-many).\",\n", "            \"What is the central node in this topology? Why is the node considered the central node?(If the central node has a name, assign a name based on the topology diagram.)\",\n", "            \"Please briefly summarize the network topology, keeping the word count within the following limits: under 100 words for fewer than 10 nodes, under 200 words for 10 to 15 nodes, and under 300 words for more than 15 nodes. The summary should include the following:\\n\\n*   **Overall Architecture:** Star, bus, ring, mesh, tree, or hybrid.\\n*   **Area/Device Description:** Describe each major area or device (e.g., servers, routers, switches, clients, etc.), including their quantity, type, and main purpose.\\n*   **Connection Methods:** Describe the connection types between areas/devices (e.g., wired, wireless, fiber optic) and connection relationships (which nodes they are connected to).\\n*   **Data Flow:** Outline how data or control information flows within the network.\\n*   **Network Goals:** Summarize the main design goals and functions of the network architecture, such as data transmission, resource sharing, secure access, etc.\\n\\nUse the following structure for your summary:\\n\\nThe network is a [Overall Architecture] topology. It consists of several interconnected areas and devices. First, [Area/Device Name 1] includes [Number] [Device Type](s) and is connected by [Connection Method]. This area's primary purpose is [Purpose of Area/Device 1]. It connects to [Area/Device Name 2] via [Connection Type], facilitating data and control flow. [Area/Device Name 2] consists of [Number] [Device Type](s) and serves as [Purpose of Area/Device 2]. Additionally, [Area/Device Name 2] connects to other areas/devices like [Area/Device Name 3], creating the overall network structure. Connections between areas/devices also include [Connection Type, e.g., firewalls, VPN connections], which provide [Purpose of Connection]. The network is designed to achieve [Overall Network Purpose, e.g., efficient data processing, secure remote access].\",\n", "        ]\n", "\n", "        max_pairs = min(num_pairs, len(question_templates))\n", "\n", "        for i in range(max_pairs):\n", "            question_template = question_templates[i]\n", "            question = question_template\n", "\n", "            content_system = (\n", "                \"You are a network topology expert assistant. \"\n", "                \"Your task is to answer questions about a network topology based on a provided image and a list of key node entities. \"\n", "                \"Instructions: \"\n", "                \"1. Carefully examine the topology image and corresponding key node entities: Understand the connections and relationships between the key node entities, and use the entity names to identify components. \"\n", "                \"2. Answer the question accurately and concisely: Provide a clear and direct answer to the question without making assumptions or introducing external information. \"\n", "                \"3. Output your answer as a string. \"\n", "                \"Topology Image: \"\n", "                \"Entities: \"\n", "                f\"{entities} \"\n", "                \"Question: \"\n", "                f\"{question}\"\n", "            )\n", "\n", "            completion = self.client.chat.completions.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": content_system},\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\"type\": \"text\", \"text\": \"Topology Image\"},\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                                },\n", "                            },\n", "                            {\"type\": \"text\", \"text\": f\"{question}\"},\n", "                        ],\n", "                    },\n", "                ],\n", "                temperature=0.2,\n", "                max_tokens=4095,\n", "            )\n", "            answer = completion.choices[0].message.content.strip()\n", "            qa_pairs.append({\"question\": question, \"answer\": answer})\n", "\n", "        return qa_pairs\n", "\n", "    def save_to_json(\n", "        self, entities: List[str], qa_pairs: List[Dict[str, str]], filename: str\n", "    ):\n", "        \"\"\"Save the extracted entities and QA pairs to a JSON file.\"\"\"\n", "        data = {\"entities\": entities, \"qa_pairs\": qa_pairs}\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(data, f, indent=4, ensure_ascii=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化 Cmanager 实例并设置网络拓扑图像路径\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["api_base = \"https://neudm.zeabur.app/v1\"\n", "api_key = \"sk-T05m0OqxOgKUjErs8c231e1c02E24573A17977F5E839E91c\"\n", "cmanager = Cmanager(api_base=api_base, api_key=api_key)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error processing file data/images/normal/.DS_Store: Error code: 400 - {'error': {'message': \"You uploaded an unsupported image. Please make sure your image has of one the following formats: ['png', 'jpeg', 'gif', 'webp']. (request id: 2025011914175694552315220243859)\", 'type': 'invalid_request_error', 'param': '', 'code': 'invalid_image_format'}}\n", "Extracted network element entities: ['Portimão', 'Old Network', 'Grindavik', '\"\\'<PERSON>grah\\'\"', '\"Vil\\'gort']\n", "Built QA pairs: [{'question': 'Please describe the overall network topology type. Is it a star, bus, ring, mesh, tree, or hybrid topology? If it is a hybrid topology, please specify the combination.', 'answer': \"The network topology depicted is a hybrid topology. It combines elements of a mesh topology, where nodes like Port<PERSON><PERSON>, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>'gort, and Grindavik are interconnected, and a star topology, where connections from Portimão extend to other nodes and the Internet.\"}, {'question': 'Please specify the total number of nodes in the network topology diagram. Subsequently, based on this total number of nodes, list the major nodes in the network topology, including their specific names or identifiers, and briefly describe the function of each major node (if the major nodes do not have names, assign names to them based on the topology diagram).', 'answer': \"The network topology diagram contains a total of 7 nodes. Here are the major nodes and their functions:\\n\\n1. **Portimão**: Acts as a central hub connecting to the Internet and other nodes. It likely manages traffic and routing.\\n\\n2. **Old Network**: Represents a legacy or existing network segment, possibly for backward compatibility or specific services.\\n\\n3. **Grindavik**: Functions as a regional node, connecting to other nodes and possibly serving local networks.\\n\\n4. **'Agrah'**: Another regional node, likely handling local traffic and customer connections.\\n\\n5. **Vil'gort**: Similar to 'Agrah', it serves as a regional node for local traffic and customer services.\\n\\n6. **Server Hotel**: Provides hosting services, with each customer getting a subnet, firewall, and server.\\n\\n7. **Internet**: Represents the external network connection, providing access to global resources.\\n\\nEach node plays a specific role in managing and routing network traffic within the topology.\"}, {'question': 'Please describe in detail the connections between the major nodes, including the connection type (e.g., wired, wireless, fiber optic) and the connection relationships (e.g., one-to-one, one-to-many, many-to-many).', 'answer': 'The network topology map shows the following connections between major nodes:\\n\\n1. **Portimão**:\\n   - Connected to the Internet via multiple WAN links (indicated by thick blue lines).\\n   - Has connections to the \"Old Network,\" Grindavik, \\'Agrah\\', and Vil\\'gort using WAN links.\\n   - The connections are likely fiber optic, given the WAN link designation.\\n\\n2. **Old Network**:\\n   - Connected to Portimão via a WAN link.\\n   - This is a one-to-one connection.\\n\\n3. **Grindavik**:\\n   - Connected to Portimão and Vil\\'gort via WAN links.\\n   - This forms a one-to-many relationship with Portimão and Vil\\'gort.\\n\\n4. **\\'Agrah\\'**:\\n   - Connected to Portimão via a WAN link.\\n   - Also connected to a server and firewall setup, indicating a one-to-many relationship.\\n\\n5. **Vil\\'gort**:\\n   - Connected to Portimão and Grindavik via WAN links.\\n   - Has a one-to-many relationship with Portimão and Grindavik.\\n   - Connected to a server and firewall setup, indicating a one-to-many relationship.\\n\\nOverall, the network uses WAN links for inter-node connections, suggesting a fiber optic or similar high-capacity medium. The topology primarily features one-to-many relationships, with Portimão acting as a central hub.'}, {'question': 'What is the central node in this topology? Why is the node considered the central node?(If the central node has a name, assign a name based on the topology diagram.)', 'answer': 'The central node in this topology is \"Portimão.\" It is considered the central node because it connects directly to multiple other nodes, including the Internet, \\'Agrah\\', Vil\\'gort, Grindavik, and the Old Network, serving as a hub for data flow within the network.'}, {'question': \"Please briefly summarize the network topology, keeping the word count within the following limits: under 100 words for fewer than 10 nodes, under 200 words for 10 to 15 nodes, and under 300 words for more than 15 nodes. The summary should include the following:\\n\\n*   **Overall Architecture:** Star, bus, ring, mesh, tree, or hybrid.\\n*   **Area/Device Description:** Describe each major area or device (e.g., servers, routers, switches, clients, etc.), including their quantity, type, and main purpose.\\n*   **Connection Methods:** Describe the connection types between areas/devices (e.g., wired, wireless, fiber optic) and connection relationships (which nodes they are connected to).\\n*   **Data Flow:** Outline how data or control information flows within the network.\\n*   **Network Goals:** Summarize the main design goals and functions of the network architecture, such as data transmission, resource sharing, secure access, etc.\\n\\nUse the following structure for your summary:\\n\\nThe network is a [Overall Architecture] topology. It consists of several interconnected areas and devices. First, [Area/Device Name 1] includes [Number] [Device Type](s) and is connected by [Connection Method]. This area's primary purpose is [Purpose of Area/Device 1]. It connects to [Area/Device Name 2] via [Connection Type], facilitating data and control flow. [Area/Device Name 2] consists of [Number] [Device Type](s) and serves as [Purpose of Area/Device 2]. Additionally, [Area/Device Name 2] connects to other areas/devices like [Area/Device Name 3], creating the overall network structure. Connections between areas/devices also include [Connection Type, e.g., firewalls, VPN connections], which provide [Purpose of Connection]. The network is designed to achieve [Overall Network Purpose, e.g., efficient data processing, secure remote access].\", 'answer': 'The network is a hybrid topology. It consists of several interconnected areas and devices. First, \"Server Hotel\" includes multiple servers and firewalls connected by LAN links. This area\\'s primary purpose is customer hosting. It connects to Portimão via WAN links, facilitating data and control flow. Portimão consists of routers and switches and serves as a central hub. Additionally, Portimão connects to other areas like Grindavik, \\'Agrah\\', and Vil\\'gort, creating the overall network structure. Connections between areas include firewalls, providing secure access. The network is designed to achieve efficient data transmission and secure customer access.'}]\n", "Results saved to data/result/en_unchecked/normal/n0001.json\n"]}], "source": ["sub_paths = [\"normal\"]\n", "data_path = \"data/images/\"\n", "res_path = \"data/result/en_unchecked/\"\n", "\n", "for sub in sub_paths:\n", "    topology_image_path = os.path.join(data_path, sub)\n", "\n", "    files = [\n", "        filename\n", "        for filename in os.listdir(topology_image_path)\n", "        if os.path.isfile(os.path.join(topology_image_path, filename))\n", "    ]\n", "    files.sort()\n", "\n", "    for i, filename in enumerate(files[0:2]):\n", "        file_path = os.path.join(topology_image_path, filename)\n", "\n", "        image_name_without_ext = os.path.splitext(filename)[0]\n", "        output_filename = f\"{image_name_without_ext}.json\"\n", "        result_dir = os.path.join(res_path, sub)\n", "\n", "        if not os.path.exists(result_dir):\n", "            os.makedirs(result_dir)\n", "\n", "        output_file_path = os.path.join(result_dir, output_filename)\n", "\n", "        # 检查输出文件是否已存在，若存在则跳过对该图像的处理\n", "        if os.path.exists(output_file_path):\n", "            print(f\"File {output_file_path} already exists. Skipping...\")\n", "            continue\n", "\n", "        # 如果文件不存在，则进行图像处理\n", "        cmanager.set_topology_image(file_path)\n", "        try:\n", "            entities = cmanager.extract_entities()\n", "            if entities:\n", "                print(\"Extracted network element entities:\", entities)\n", "                qa_pairs = cmanager.build_qa_pairs(entities)\n", "                print(\"Built QA pairs:\", qa_pairs)\n", "\n", "                cmanager.save_to_json(entities, qa_pairs, output_file_path)\n", "                print(f\"Results saved to {output_file_path}\")\n", "        except Exception as e:\n", "            print(f\"Error processing file {file_path}: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}