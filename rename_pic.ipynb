{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Renamed 'a_0056.png' to 'a0001.png'\n", "Renamed 'a_0042.png' to 'a0002.png'\n", "Renamed 'a_0081.png' to 'a0003.png'\n", "Renamed 'a_0095.JPG' to 'a0004.JPG'\n", "Renamed 'a_0010.webp' to 'a0005.webp'\n", "Renamed 'a_0122.png' to 'a0006.png'\n", "Renamed 'a_0080.png' to 'a0007.png'\n", "Renamed 'a_0043.png' to 'a0008.png'\n", "Renamed 'a_0109.jpg' to 'a0009.jpg'\n", "Renamed 'a_0121.png' to 'a0010.png'\n", "Renamed 'a_0109.png' to 'a0011.png'\n", "Renamed 'a_0006.jfif' to 'a0012.jfif'\n", "Renamed 'a_0040.png' to 'a0013.png'\n", "Renamed 'a_0007.jfif' to 'a0014.jfif'\n", "Renamed 'a_0044.png' to 'a0015.png'\n", "Renamed 'a_0050.png' to 'a0016.png'\n", "Renamed 'a_0093.png' to 'a0017.png'\n", "Renamed 'a_0093.jpg' to 'a0018.jpg'\n", "Renamed 'a_0087.jpg' to 'a0019.jpg'\n", "Renamed 'a_0078.jpg' to 'a0020.jpg'\n", "Renamed 'a_0124.jpg' to 'a0021.jpg'\n", "Renamed '.DS_Store' to 'a0022'\n", "Renamed 'a_0079.jpg' to 'a0023.jpg'\n", "Renamed 'a_0086.jpg' to 'a0024.jpg'\n", "Renamed 'a_0092.png' to 'a0025.png'\n", "Renamed 'a_0051.png' to 'a0026.png'\n", "Renamed 'a_0045.png' to 'a0027.png'\n", "Renamed 'a_0079.png' to 'a0028.png'\n", "Renamed 'a_0053.png' to 'a0029.png'\n", "Renamed 'a_0084.jpg' to 'a0030.jpg'\n", "Renamed 'a_0090.jpg' to 'a0031.jpg'\n", "Renamed 'a_0106.jpeg' to 'a0032.jpeg'\n", "Renamed 'a_0085.png' to 'a0033.png'\n", "Renamed 'a_0091.png' to 'a0034.png'\n", "Renamed 'a_0038.gif' to 'a0035.gif'\n", "Renamed 'a_0046.png' to 'a0036.png'\n", "Renamed 'a_0052.png' to 'a0037.png'\n", "Renamed 'a_0021.png' to 'a0038.png'\n", "Renamed 'a_0009.png' to 'a0039.png'\n", "Renamed 'a_0008.png' to 'a0040.png'\n", "Renamed 'a_0020.png' to 'a0041.png'\n", "Renamed 'a_0034.png' to 'a0042.png'\n", "Renamed 'a_0022.png' to 'a0043.png'\n", "Renamed 'a_0036.png' to 'a0044.png'\n", "Renamed 'a_0037.png' to 'a0045.png'\n", "Renamed 'a_0023.png' to 'a0046.png'\n", "Renamed 'a_0033.png' to 'a0047.png'\n", "Renamed 'a_0032.png' to 'a0048.png'\n", "Renamed 'a_0026.png' to 'a0049.png'\n", "Renamed 'a_0030.png' to 'a0050.png'\n", "Renamed 'a_0001.jfif' to 'a0051.jfif'\n", "Renamed 'a_0024.png' to 'a0052.png'\n", "Renamed 'a_0025.png' to 'a0053.png'\n", "Renamed 'a_0031.png' to 'a0054.png'\n", "Renamed 'a_0019.png' to 'a0055.png'\n", "Renamed 'a_0002.jfif' to 'a0056.jfif'\n", "Renamed 'a_0014.png' to 'a0057.png'\n", "Renamed 'a_0020 copy.png' to 'a0058.png'\n", "Renamed 'a_0001.png' to 'a0059.png'\n", "Renamed 'a_0015.png' to 'a0060.png'\n", "Renamed 'a_0003.png' to 'a0061.png'\n", "Renamed 'a_0017.png' to 'a0062.png'\n", "Renamed 'a_0016.png' to 'a0063.png'\n", "Renamed 'a_0002.png' to 'a0064.png'\n", "Renamed 'a_0006.png' to 'a0065.png'\n", "Renamed 'a_0012.png' to 'a0066.png'\n", "Renamed 'a_0023.jfif' to 'a0067.jfif'\n", "Renamed 'a_0013.png' to 'a0068.png'\n", "Renamed 'a_0007.png' to 'a0069.png'\n", "Renamed 'a_0011.png' to 'a0070.png'\n", "Renamed 'a_0005.png' to 'a0071.png'\n", "Renamed 'a_0039.jfif' to 'a0072.jfif'\n", "Renamed 'a_0003.jfif' to 'a0073.jfif'\n", "Renamed 'a_0004.png' to 'a0074.png'\n", "Renamed 'a_0010.png' to 'a0075.png'\n", "Renamed 'a_0038.png' to 'a0076.png'\n", "Renamed 'a_0063.png' to 'a0077.png'\n", "Renamed 'a_0008.jfif' to 'a0078.jfif'\n", "Renamed 'a_0077.jpg' to 'a0079.jpg'\n", "Renamed 'a_0103.jpg' to 'a0080.jpg'\n", "Renamed 'a_0076.jpg' to 'a0081.jpg'\n", "Renamed 'a_0089.png' to 'a0082.png'\n", "Renamed 'a_0062.png' to 'a0083.png'\n", "Renamed 'a_0060.png' to 'a0084.png'\n", "Renamed 'a_0004.jfif' to 'a0085.jfif'\n", "Renamed 'a_0074.png' to 'a0086.png'\n", "Renamed 'a_0048.png' to 'a0087.png'\n", "Renamed 'a_0048.jpg' to 'a0088.jpg'\n", "Renamed 'a_0100.jpg' to 'a0089.jpg'\n", "Renamed 'a_0012.jfif' to 'a0090.jfif'\n", "Renamed 'a_0100.png' to 'a0091.png'\n", "Renamed 'a_0101.png' to 'a0092.png'\n", "Renamed 'a_0101.jpg' to 'a0093.jpg'\n", "Renamed 'a_0049.png' to 'a0094.png'\n", "Renamed 'a_0075.png' to 'a0095.png'\n", "Renamed 'a_0071.png' to 'a0096.png'\n", "Renamed 'a_0105.jpg' to 'a0097.jpg'\n", "Renamed 'a_0013.jfif' to 'a0098.jfif'\n", "Renamed 'a_0104.jpg' to 'a0099.jpg'\n", "Renamed 'a_0070.png' to 'a0100.png'\n", "Renamed 'a_0005.jfif' to 'a0101.jfif'\n", "Renamed 'a_0064.png' to 'a0102.png'\n", "Renamed 'a_0058.png' to 'a0103.png'\n", "Renamed 'a_0072.png' to 'a0104.png'\n", "Renamed 'a_0066.png' to 'a0105.png'\n", "Renamed 'a_0099.jpg' to 'a0106.jpg'\n", "Renamed 'a_0025.jfif' to 'a0107.jfif'\n", "Renamed 'a_0112.jpg' to 'a0108.jpg'\n", "Renamed 'a_0106.jpg' to 'a0109.jpg'\n", "Renamed 'a_0107.png' to 'a0110.png'\n", "Renamed 'a_0107.jpg' to 'a0111.jpg'\n", "Renamed 'a_0009.jfif' to 'a0112.jfif'\n", "Renamed 'a_0067.png' to 'a0113.png'\n", "Renamed 'a_0073.png' to 'a0114.png'\n"]}], "source": ["import os\n", "\n", "\n", "def rename_images(directory):\n", "    files = os.listdir(directory)\n", "    counter = 1\n", "    prefix = \"a\"\n", "\n", "    for file in files:\n", "        file_path = os.path.join(directory, file)\n", "        if os.path.isfile(file_path):\n", "            name, ext = os.path.splitext(file)\n", "            # 检查后缀名是否为jiff，如果是，则更改为jpg\n", "            if ext.lower() == \".jfif\":\n", "                ext = \".jpg\"\n", "            new_name = f\"{prefix}{str(counter).zfill(4)}{ext}\"\n", "            new_file_path = os.path.join(directory, new_name)\n", "            os.rename(file_path, new_file_path)\n", "            print(f\"Renamed '{file}' to '{new_name}'\")\n", "            counter += 1\n", "\n", "\n", "rename_images(\"/Users/<USER>/Desktop/Topo2text/data/images/discard\")"]}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}