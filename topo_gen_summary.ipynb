{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import base64\n", "import requests\n", "import os\n", "import json\n", "from PIL import Image\n", "\n", "\n", "api_base = \"https://neudm.zeabur.app/v1\"\n", "api_key = \"实际key\"\n", "# image_path = \"data/images/normal\"\n", "# json_path = \"normal\"\n", "# output_path = \"data/res_summary_len8\"\n", "image_path = \"/Users/<USER>/Desktop/topo2text/data/test_data/normal\"\n", "json_path = \"/Users/<USER>/Desktop/topo2text/data/result/test_data/normal\"\n", "output_path = \"data/res_summary_test\"\n", "# with open('data/result/normal/n1058.json', 'r') as file:\n", "#     QA_pairs = json.load(file)\n", "if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Summary for n1059.png saved to n1059.txt\n", "Summary for n1058.png saved to n1058.txt\n", "Summary for n1062.png saved to n1062.txt\n", "Summary for n1060.jpg saved to n1060.txt\n", "Summary for n1061.jpg saved to n1061.txt\n"]}], "source": ["def encode_image(image_path):\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "\n", "def get_image_summary(base64_image, json_data=None):\n", "\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {api_key}\",\n", "    }\n", "\n", "    # 删除QA pairs相关内容，测试发现，其实我们并不需要QA pair来帮助他总结，此处QA pair是作为额外的训练数据的\n", "    # TODO 丰富4内容，增加更多信息，以及如何处理QA pairs\n", "    # 写成 ： 这个summary要以这样的形式：1.先两个句子概括整个拓扑图的结构和大致内容，2. 3-4个句子介绍拓扑图的细节. 3. 两句句子描述拓扑图的功能\n", "    content_system = (\n", "        \"You are a professional network topology analysis expert.\"\n", "        \"Your goal is to accurately and clearly analyze and summarize a topology image\\n\"\n", "        \"You will be provided two information:\"\n", "        \"1. A topology image \"\n", "        \"2. (Optional) QA pairs related to the main elements of the topology. \\n\"\n", "        \"Please follow these steps: \"\n", "        \"1. Image Analysis: Examine the topology image closely, identifying key nodes and their connections. \"\n", "        \"2. Description Method Selection:** Choose a **layered description** (suitable for topologies with clear hierarchies, such as core and access layers) or a **distributed description** (suitable for topologies with scattered nodes) based on the observed structure.\"\n", "        \"3. Generate Summary: Using the chosen description method, create a summary of the topology. \"\n", "        \"Output only an English summary of the topology image without any further analysis. The format should be: \"\n", "        \"Output format: This network employs a [Topology type, e.g., ‘star,’ ‘ring,’ ‘tree,’ or ‘mesh’] architecture, primarily designed to [State the primary purpose of the network in one sentence, e.g., ‘facilitate internal communication,’ ‘provide secure remote access,’ ‘host web applications’]. Its core nodes include: [List core nodes, e.g., ‘central switches, routers, application servers, database servers’], interconnected and collaborating closely. For instance, [Node 1, e.g., ‘the front-end server’] interacts with [Node 2, e.g., ‘the load balancer’] via [Connection method, e.g., ‘HTTP requests’], responsible for [Node 1’s function, e.g., ‘handling incoming requests’]; [Node 2, e.g., ‘the load balancer’] forwards requests to [Node 3, e.g., ‘the application server’] through [Connection method, e.g., ‘TCP connections’], enabling [Node 2’s function, e.g., ‘load distribution’]; [Node 3, e.g., ‘the application server’] retrieves data from [Node 4, e.g., ‘the database server’] using [Connection method, e.g., ‘database connections’], fulfilling [Node 3’s function, e.g., ‘data processing’]; and [Node 5, e.g., ‘the monitoring server’] gathers status information via [Connection method, e.g., ‘SNMP protocol’], enabling [Node 5’s function, e.g., ‘system monitoring’]. These nodes are linked through [Specify the dependencies between nodes, e.g., ‘data dependencies, control dependencies, and collaborative dependencies’]. Overall, this topology aims to [Summarize the overall purpose of the network topology, emphasizing how it meets network goals, e.g., ‘improve system availability,’ ‘ensure secure deployment,’ ‘flexibly accommodate future growth,’ ‘guarantee reliable information flow,’ ‘provide a stable service’].\"\n", "    )\n", "\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": content_system},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": f\"<Topo_image>, QA pairs: {json_data}.\",\n", "                },\n", "                {\n", "                    \"type\": \"image_url\",\n", "                    \"image_url\": {\"url\": f\"data:image/jpeg;base64,{base64_image}\"},\n", "                },\n", "            ],\n", "        },\n", "    ]\n", "\n", "    payload = {\n", "        \"model\": \"gpt-4o-mini\",\n", "        \"messages\": messages,\n", "        \"temperature\": 0.2,\n", "        # \"max_tokens\": 300,\n", "    }\n", "    try:\n", "        response = requests.post(\n", "            f\"{api_base}/chat/completions\", headers=headers, json=payload\n", "        )\n", "        response.raise_for_status()  \n", "        result = response.json()\n", "        summary = result[\"choices\"][0][\"message\"][\"content\"]\n", "        return summary\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        print(f\"Response content: {response.text}\")\n", "        return None\n", "    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, json.JSONDecodeError) as e:\n", "        print(f\"Error processing response: {e}\")\n", "        return None\n", "\n", "\n", "# 遍历图片目录并处理每个图片\n", "for filename in os.listdir(image_path):\n", "    if filename.lower().endswith((\".png\", \".jpg\", \".jpeg\", \".gif\", \".bmp\", \".webp\")):\n", "        image_filepath = os.path.join(image_path, filename)\n", "        json_filename = os.path.splitext(filename)[0] + \".json\"\n", "        json_filepath = os.path.join(json_path, json_filename)\n", "\n", "        # 读取JSON文件内容（如果存在）\n", "        json_data = None\n", "        if os.path.exists(json_filepath):\n", "            try:\n", "                with open(json_filepath, \"r\", encoding=\"utf-8\") as json_file:\n", "                    json_data = json.load(json_file)\n", "            except json.JSONDecodeError as e:\n", "                print(f\"Error decoding JSON from {json_filepath}: {e}\")\n", "                continue\n", "\n", "        # 编码图片并获取总结\n", "        base64_image = encode_image(image_filepath)\n", "        if base64_image:\n", "            summary = get_image_summary(base64_image, json_data)\n", "            if summary:\n", "                txt_filename = os.path.splitext(filename)[0] + \".txt\"\n", "                txt_filepath = os.path.join(output_path, txt_filename)\n", "                try:\n", "                    with open(txt_filepath, \"w\", encoding=\"utf-8\") as txt_file:\n", "                        txt_file.write(summary)\n", "                    print(f\"Summary for {filename} saved to {txt_filename}\")\n", "                except Exception as e:\n", "                    print(f\"Error saving summary for {filename}: {e}\")\n", "        else:\n", "            print(f\"Skipping invalid or unsupported image file: {filename}\")\n", "    else:\n", "        print(f\"Skipping non-image file: {filename}\")"]}], "metadata": {"kernelspec": {"display_name": "topo_qwen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}